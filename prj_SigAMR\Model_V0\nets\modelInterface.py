# =======================================================================================================================
#   Function    ：modelInterface.py
#   Description : 模型总体集成类
# 
#   Parameter   : 
#   Author      : Liuzhiguo
#   Date        : 2025-07-11
# =======================================================================================================================
import math
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.nn import Module, Parameter

from nets.iresnet import (iresnet18, iresnet34, iresnet50, iresnet100, iresnet200)
from nets.mobilefacenet import get_mbf
from nets.mobilenet import get_mobilenet
from nets.SigAMRNet import SigAMRNet 
from nets.swin_transformer import swin_transformer_tiny
from nets.Module_attentions import SE_Row, SE_RowV1
    
class modelInterface(nn.Module):
    '''
        mode interface defination
    '''
    def __init__(self, num_classes=None, backbone="mobilefacenet", pretrained=False):
        super(modelInterface, self).__init__()
        self.in_ch = 8
        #self.weights = torch.tensor([0.5, 0.4, 0.1], requires_grad=True)
        self.weights = [0.2, 0.4, 0.3, 0.1]
        #self.max_pooling_layer = nn.MaxPool2d(kernel_size=(self.in_ch, 1), stride=(self.in_ch, 1))
        #self.max_pooling_layer = nn.AdaptiveMaxPool2d([128, 116])
        self.conv_pooling_layer = nn.Conv2d(1, 1, kernel_size=[self.in_ch,1], stride=[self.in_ch, 1], bias=False)
        self.conv_emb = nn.Conv2d(self.in_ch, self.in_ch, kernel_size=[3,3], padding=1) #向量嵌入
        self.se_row_layer = SE_Row(in_chnls=3, ratio=1)  # 平均值加权
        #self.se_row_layer = SE_RowV1(in_chnls=3, ratio=1) # 平均值和最大值加权
        if backbone=="mobilefacenet":
            embedding_size  = 128
            s               = 32
            self.dmodel    = get_mbf(embedding_size=embedding_size, pretrained=pretrained)

        elif backbone=="mobilenetv1":
            embedding_size  = 512
            s               = 64
            self.dmodel    = get_mobilenet(dropout_keep_prob=0.5, embedding_size=embedding_size, pretrained=pretrained)

        elif backbone=="iresnet18":
            embedding_size  = 512
            s               = 64
            self.dmodel    = iresnet18(dropout_keep_prob=0.5, embedding_size=embedding_size, pretrained=pretrained)

        elif backbone=="iresnet34":
            embedding_size  = 512
            s               = 64
            self.dmodel    = iresnet34(dropout_keep_prob=0.5, embedding_size=embedding_size, pretrained=pretrained)

        elif backbone=="iresnet50":
            embedding_size  = 512
            s               = 64
            self.dmodel    = iresnet50(dropout_keep_prob=0.5, embedding_size=embedding_size, pretrained=pretrained)

        elif backbone=="iresnet100":
            embedding_size  = 512
            s               = 64
            self.dmodel    = iresnet100(dropout_keep_prob=0.5, embedding_size=embedding_size, pretrained=pretrained)

        elif backbone=="iresnet200":
            embedding_size  = 512
            s               = 64
            self.dmodel    = iresnet200(dropout_keep_prob=0.5, embedding_size=embedding_size, pretrained=pretrained)
        elif backbone=="SigAMRNet":
            embedding_size  = 64#96#512
            s               = 64
            self.dmodel    = SigAMRNet(embedding_size=embedding_size) 
        elif backbone=="swin_transformer_tiny":
            embedding_size  = 512
            s               = 64
            self.dmodel    = swin_transformer_tiny(input_shape=[1024, 45],pretrained=False,num_classes=embedding_size)           
        else:
            raise ValueError('Unsupported backbone - `{}`, Use mobilefacenet, mobilenetv1.'.format(backbone))

        #分类头 (分类用)
        self.ClsHead = nn.Sequential(
            nn.Dropout(p=0),#0.2
            nn.Linear(embedding_size, embedding_size//2),
            #note:4/10 尝试引入非线性
            nn.ELU(),
            nn.Linear(embedding_size//2, num_classes),
        )

    # todo:2025/4/8: 这里尝试对时域信号进行归一化处理 看会不会最后的结果好一点
    #note:这里是功率归一化，结果显示可以
    def Temporal_normalization(self, rx_signals):
        signal_power = torch.sum((rx_signals[:, 0, :, :] ** 2 + rx_signals[:, 1, :, :] ** 2), dim=-1) / 2  #explain:得到信号的平均功率  所以这里要/2
        signal = rx_signals * math.sqrt(1) / torch.sqrt(signal_power.unsqueeze(-1).unsqueeze(-1))
        #todo：这里可以尝试下再减去一个均值20250421(就是实部虚部分别减去当前能量归一化后的均值) 结论：影响不大 先不加
        #signal = signal - torch.mean(signal, dim=1).unsqueeze(1)
        return signal
    		
    def forward(self, x):
        # x = self.Temporal_normalization(x) # 对信号进行归一化处理

        x = self.dmodel(x) # 网络处理
        x = x.view(x.size()[0], -1)

        x = F.normalize(x, dim=1) #dmodel-loss这里要求是对归一化之后的特征进行处理
        x = self.ClsHead(x) # [batch self.nclass] 

        return x
