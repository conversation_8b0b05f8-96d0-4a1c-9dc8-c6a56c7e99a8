import os
import numpy as np
import torch
import torch.utils.data as data
import h5py
import soundfile
from torch.utils.data import Dataset
import pandas as pd
from sklearn.model_selection import train_test_split
from collections import defaultdict

gLenNorm = 192 # 61.44M采样, 数据速率为80K, 数据点数:61.44e6/80e3=768, 如为15.36M采样，则数据点数：15.36e6/80e3=192，但我们用窄带滤波的方式，最后统一为 192 

def getLenNorm():
    '''
    获取归一化单条数据长度
    '''
    return gLenNorm

def LoadHdfsDataset(dataset_file):
    '''
    加载单个hdfs数据集
    '''
    LenNorm     = getLenNorm()

    if os.path.exists(dataset_file)==False:
        print("{0}:文件不存在".format(dataset_file))
        return
    
    f = h5py.File(dataset_file, 'r')
    rx_signals  = f['rx_signal'][:]
    class_ids   = f['class_id'][:]
    class_names = f['class_name'][:]
    fs_values = f['fs'][:]
    bw_values = f['bw'][:]
    f.close()

    # 裁剪数据集的size
    # N_row =  rx_signals.shape[0]
    # N_col =  rx_signals.shape[1]
    # if N_col<LenNorm:
    #     rx_signals = np.pad(rx_signals,((0,0),(0,LenNorm-N_col),(0,0)), 'constant')
    # else:
    #     rx_signals = np.resize(rx_signals,(N_row, LenNorm, 2))
    
    # print('rx_signal:', rx_signals.shape, rx_signals.dtype)
    # print('class_id:', class_ids.shape, class_ids.dtype)
    return  rx_signals, class_ids, class_names, fs_values, bw_values


def read_hdf5_dataset(file_path):
    """读取HDF5数据集并进行预处理"""
    with h5py.File(file_path, 'r') as f:
        X = f['signal_data'][:]  # [len, 2, 样本数]
        Y = f['labels'][:]  # 标签 [1, 样本数]
        Z = f['SNRs'][:]  # SNR值 [1, 样本数]
        modulations = f.attrs['modulations']
        
        # 转换数据形状为[样本数, 2, len]并归一化
        X = np.transpose(X, (2, 1, 0)) #[样本数, 2, len]
        X = X[:, :, np.newaxis, :] # [样本数, 2, 1, len]
        # max_vals = np.max(np.sqrt(X[:,0,:,:]**2+X[:,1,:,:]**2),axis=2)
        # X[:,0,0,:] = (X[:,0,0,:] / max_vals).astype(np.float32)
        # X[:,1,0,:] = (X[:,1,0,:] / max_vals).astype(np.float32)

        # 将Y和Z转换为一维数组
        Y = Y.flatten()
        Z = Z.flatten()

        nstep = int(61.44e6//15.36e6)
        X = X[:,:,:,::nstep] #下抽样,[样本数, 2, 1, len]
        
    return X, Y, Z, modulations

def stratified_split(X, Y, Z, modulations, test_size=0.2, val_size=0.2, random_state=42):
    """
    按调制类型和SNR分层划分数据集
    
    参数:
    - X: 特征数据 [样本数, 128, 2]
    - Y: 标签数组 [样本数]
    - Z: SNR值数组 [样本数]
    - modulations: 调制类型列表
    - test_size: 测试集比例
    - val_size: 验证集比例(相对于训练集的比例)
    - random_state: 随机种子
    
    返回:
    - 划分后的数据集 (X_train, Y_train, Z_train), (X_val, Y_val, Z_val), (X_test, Y_test, Z_test)
    """
    # 创建DataFrame便于处理
    df = pd.DataFrame({
        'modulation': Y,
        'snr': Z,
        'index': np.arange(len(Y))
    })
    
    # 初始化存储各子集索引的列表
    train_indices = []
    val_indices = []
    test_indices = []
    
    # 对每个调制类型和SNR组合进行分层抽样
    for mod in df['modulation'].unique():
        for snr in df[df['modulation'] == mod]['snr'].unique():
            # 获取当前mod和snr组合的所有索引
            group = df[(df['modulation'] == mod) & (df['snr'] == snr)]
            indices = group['index'].values
            
            # 划分当前组
            if len(indices) > 1:  # 确保有足够样本可以划分
                # 先划分出测试集
                idx_train_val, idx_test = train_test_split(
                    indices, 
                    test_size=test_size, 
                    random_state=random_state
                )
                
                # 再从训练验证集中划分验证集
                idx_train, idx_val = train_test_split(
                    idx_train_val,
                    test_size=val_size,
                    random_state=random_state
                )
                
                train_indices.extend(idx_train)
                val_indices.extend(idx_val)
                test_indices.extend(idx_test)
            else:
                # 如果样本太少，全部放入训练集
                train_indices.extend(indices)
    
    # 根据索引获取数据
    X_train, Y_train, Z_train = X[train_indices], Y[train_indices], Z[train_indices]
    X_val, Y_val, Z_val = X[val_indices], Y[val_indices], Z[val_indices]
    X_test, Y_test, Z_test = X[test_indices], Y[test_indices], Z[test_indices]
    
    return (X_train, Y_train, Z_train), (X_val, Y_val, Z_val), (X_test, Y_test, Z_test)

class SigAMRDataset(data.Dataset):
    '''
       AMR:Automatic Demodulation Recognization 自动调制方式识别数据集
    '''
    def LoadDataset_fromHdfs(self, dataset_file):
        '''
            从hdfs数据集中读取
        '''
        rx_signals_in, class_ids, class_names, fs_values, bw_values  = LoadHdfsDataset(dataset_file)
        if fs_values[0] > 15.36e6:
            nstep = int(fs_values[0]//15.36e6)
            rx_signals = rx_signals_in[:,::nstep,:] #下抽样,[b, len, 2]
        else:
            rx_signals = rx_signals_in


        LenNorm     = getLenNorm()
        N_row =  rx_signals.shape[0]
        N_col =  rx_signals.shape[1]
        
        if N_col<LenNorm:
            rx_signals = np.pad(rx_signals,((0, N_row), (0,LenNorm-N_col),(0,0)), 'constant')
        else:
            rx_signals = np.resize(rx_signals,(N_row, LenNorm, 2))
        
        rx_signals = rx_signals.transpose(0, 2 ,1)  #[b 2 len]
        rx_signals = rx_signals[:, :, np.newaxis, :]#[b 2 1 len]
        return (rx_signals, class_ids)


    def __init__(self, dataset_file):
        self.rx_signals, self.class_ids = self.LoadDataset_fromHdfs(dataset_file)
        
    def __init__(self, signal_in):
        self.rx_signals, self.class_ids = signal_in

    def __len__(self):
        return len(self.class_ids)

    def __getitem__(self, index):  
        return self.rx_signals[index], self.class_ids[index]

def SigAMRDataset_collate(batch):
    '''
        AMRDataset collate
    '''
    rx_signals  = []
    targets = []
    for rx_signal, label in batch:
        rx_signals.append(rx_signal)
        targets.append(label)

    rx_signals  = torch.tensor(np.array(rx_signals))
    targets = torch.tensor(np.array(targets)).long()

    #targets = torch.cat(targets, 0)
    return rx_signals, targets
    

class SigNbDataset(data.Dataset):
    def __init__(self, lines):
        self.lines       = lines
        self.LenNorm     = getLenNorm()

    def __len__(self):
        return len(self.lines)

    def __getitem__(self, index):
        annotation_path = self.lines[index].split(';')[1].split()[0]
        label               = int(self.lines[index].split(';')[0])
        
        #rx_signal, class_id, class_name, fs_value, bw_value = LoadHdfsDataset(annotation_path)
        rx_signals, fs_value = soundfile.read(annotation_path, dtype='float32')#(60000, 2)
        N_row =  rx_signals.shape[0]
        if N_row<self.LenNorm:
            rx_signals = np.pad(rx_signals,((0,self.LenNorm-N_row),(0,0)), 'constant')
        else:
            rx_signals = np.resize(rx_signals,(self.LenNorm, 2))

        #rx_signal = rx_signal[0:self.LenNorm,:]
        if rx_signals.shape[0]!=self.LenNorm or rx_signals.shape[1]!=2 or len(rx_signals.shape)!=2: #(1, 59392, 2)
             print(fs_value)

        # if label != class_id:
        #     print(fs_value)    
        return rx_signals, label

def SigNbDataset_collate(batch):
    rx_signals  = []
    targets = []
    for rx_signal, label in batch:
        rx_signals.append(rx_signal)
        targets.append(label)

    rx_signals  = torch.tensor(np.array(rx_signals))#torch.from_numpy(np.array(rx_signals))#.type(torch.FloatTensor)
    targets = torch.tensor(np.array(targets)).long()#torch.from_numpy(np.array(targets)).long()

    #targets = torch.cat(targets, 0)
    return rx_signals, targets
    
def dataset_collate(batch):
    images  = []
    targets = []
    for image, y in batch:
        images.append(image)
        targets.append(y)
    images  = torch.from_numpy(np.array(images)).type(torch.FloatTensor)
    targets = torch.from_numpy(np.array(targets)).long()
    return images, targets

#labeled signal wild
class LSWDataset(data.Dataset):
    def __init__(self, lines_lsw):
        self.lines_lsw = lines_lsw 
        self.LenNorm     = getLenNorm()

    def __getitem__(self, index):
        line    = self.lines_lsw[index].replace("\n", "")#移除换行符
        (id1, path_1, id2, path_2) = line.split(';')
        issame = (id1==id2)
        #rx_signal1, class_id, class_name, fs_value, bw_value = LoadHdfsDataset(path_1)
        #rx_signal2, class_id, class_name, fs_value, bw_value = LoadHdfsDataset(path_2)
        rx_signal1, fs_value = soundfile.read(path_1, dtype='float32')
        rx_signal2, fs_value = soundfile.read(path_2, dtype='float32')
        #LenNorm = 1024*58+512
        N_row =  rx_signal1.shape[0]
        if N_row<self.LenNorm:
            rx_signal1 = np.pad(rx_signal1,((0,self.LenNorm-N_row),(0,0)), 'constant')
        else:
            rx_signal1 = np.resize(rx_signal1,(self.LenNorm, 2))
        
        N_row =  rx_signal2.shape[0]
        if N_row<self.LenNorm:
            rx_signal2 = np.pad(rx_signal2,((0,self.LenNorm-N_row),(0,0)), 'constant')
        else:
            rx_signal2 = np.resize(rx_signal2,(self.LenNorm, 2))

        # rx_signal1 = rx_signal1[0:self.LenNorm,:]
        # rx_signal2 = rx_signal2[0:self.LenNorm,:]

        return rx_signal1, rx_signal2, issame

    def __len__(self):
        return len(self.lines_lsw)
    
class MDataHandler(Dataset):
    def __init__(self, rx_signal,class_id,class_name,fs_value, bw_value):
        self.rx_signal = rx_signal
        self.class_id = class_id
        self.class_name = class_name
        self.fs_value = fs_value        
        self.bw_value = bw_value

    def __getitem__(self, index):
        return torch.tensor(self.rx_signal[index]), torch.tensor(self.fs_value[index]), torch.tensor(self.bw_value[index]),torch.tensor(self.class_id[index])

    def __len__(self):
        return len(self.class_id)

class Epoch_params():
    def __init__(self, epoch, total_epoch, num_train,num_val,batch_size):
        self.epoch = epoch
        self.total_epoch = total_epoch
        self.batch_size = batch_size
        self.epoch_step = num_train // batch_size
        self.epoch_step_val = num_val // batch_size


# Data Loader Class Defining
class DatasetFolder_eval(Dataset):
    def __init__(self, y, fs_value, bw_value):
        self.y = y.astype(np.float32)
        self.fs_value = fs_value.astype(np.float32)
        self.bw_value = bw_value.astype(np.float32)

    def __getitem__(self, index):
        return torch.tensor(self.y[index]), torch.tensor(self.fs_value[index]), torch.tensor(self.bw_value[index])

    def __len__(self):
        return len(self.y)
        
# 无人机信号数据集        
class DatasetDroneSig(Dataset):
    def __init__(self, y):
        self.y = y.astype(np.float32)
        self.datalen = 160000
        
    def __getitem__(self, index):        
        return torch.tensor(self.y[index])

    def __len__(self):
        return len(self.y)

def LoadSpecialMultiHdfsDataset(folders:list[str], sProp):
    '''
    加载多个数据集
    '''
    dataset_files = []
    ext_name = 'hdf5'
    for folder in folders:
        for file in os.listdir( folder ):
            if file.endswith( ext_name ) and sProp in file:# os.path.splitext(name)[1] == suffix:
                dataset_file = os.path.join(folder, file)
                dataset_files.append(dataset_file) #数据集
    
    rx_signals = []
    class_ids = []
    class_names = []
    fs_values = []
    bw_values = []
    for dataset_file in dataset_files:
        rx_signal, class_id, class_name, fs_value, bw_value = LoadHdfsDataset(dataset_file) # rx_signal: nRows, length, I/Q
        rx_signals.append(rx_signal)
        class_ids.append(class_id)
        class_names.append(class_name)
        fs_values.append(fs_value)
        bw_values.append(bw_value)     

    rx_signals = np.concatenate(rx_signals,axis=0)
    class_ids = np.concatenate(class_ids,axis=0)
    class_names = np.concatenate(class_names,axis=0)
    fs_values = np.concatenate(fs_values,axis=0)
    bw_values = np.concatenate(bw_values,axis=0)

    return rx_signals, class_ids, class_names, fs_values, bw_values