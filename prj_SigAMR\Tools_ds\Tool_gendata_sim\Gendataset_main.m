
% ================================ 利用matlab仿真生成五种调制方式数据集=======================

% ------------------------------- '0:BPSK, 1:QPSK, 2:16QAM, 3:GFSK, 4:LoRa' -----------------

% ------------------------------- 按照RML数据集结构保存为HDF5文件 ----------------------------

% ------------------------------------2025.7.18----------------------------------------------

%                               仿真生成数据集流程:  
%                                 1. 参数设置；
%                                 2. 初始化存储；
%                                 3. 生成信号；
%                                 4. 存储为数据集

clc
clear all

%% 1. 添加路径 + 参数配置
IncludePaths;
datasetconfig;

% 1.1 信号参数设置
num_samples = myconfig.num_samples;         % 每种调制类型每种SNR的样本数
snr_levels = myconfig.snr_levels;           % 信噪比范围(dB)
bw_all = myconfig.bw;                           % 带宽
fs = myconfig.fs;                           % 采样率
Rb = myconfig.Rb;                           % 码元速率
sps_all = myconfig.sps;                         % BPSK、QPSK、GFSK和16QAM 每符号采样数 
spf = myconfig.spf;                         % 初始信号长度
crop_length = myconfig.crop_length;         % 随机截取的信号长度
fc = myconfig.fc;                           % 载波频率
sf = myconfig.sf;                           % 扩频因子
modsignal_len = spf .* sps_all;                 % 除lora外的调制方式初始信号长度
channelization = myconfig.channelization;   % 是否进行信道化(下采样、变频)
nb_bw = myconfig.nb_bw;                     % 窄带带宽
nb_fc = myconfig.nb_fc;                     % 窄带载波频率
nb_fs = myconfig.nb_fs;                     % 窄带采样频率

% 1.2 调制类型的定义
modulationTypes = myconfig.modulationTypes;
mod_type_cell = myconfig.mod_type_cell;
label_map = containers.Map(mod_type_cell, 0:length(modulationTypes) - 1); % 创建调制类型到标签的映射
num_modulations = length(mod_type_cell);
%% 2 初始化存储
% 2.1 计算样本总数
total_samples = num_samples * num_modulations * length(snr_levels);

% 2.2 训练集验证集测试集比例
train_ratio = myconfig.train_ratio;
val_ratio = myconfig.val_ratio;
test_ratio = myconfig.test_ratio;

% 2.3 初始化存储数据、标签和信噪比
data = zeros(total_samples, 2, crop_length);        % I/Q数据，随机截取768个点
labels = zeros(total_samples, 1);                   % 调制类型标签
snr_values = zeros(total_samples, 1);               % SNR值

%% 3 生成信号(按照每类每信噪比的方式生成)
sample_idx = 1;
rng(42);                                            % 设置随机种子保证可重复性
tic
% 3.1 生成每种调制类型的信号
for mod_idx = 1:num_modulations

    label_value = label_map(string(mod_type_cell(mod_idx))); % 获取当前调制类型的十进制标签
    mod_type = modulationTypes(mod_idx);
    fprintf('Generating %s signals...\n', mod_type);

% 3.2 生成每种信噪比的信号
    for snr_idx = 1:length(snr_levels)
        snr = snr_levels(snr_idx);   
        % 定义信道模型
        channel = helperModClassTestChannel(...
          'SampleRate', fs, ...
          'SNR', snr, ...
          'PathDelays', [0 1.8 3.4] / fs, ...
          'AveragePathGains', [0 -2 -10], ...
          'KFactor', 4, ...
          'MaximumDopplerShift', 4, ...
          'MaximumClockOffset', 5, ...
          'CenterFrequency', fs);

% 3.3 每类每信噪比下生成 num_samples 条数据     
        for sample_num = 1:num_samples
            idx = randi([1,3], 1);
            sps = sps_all(idx);
            bw = bw_all(idx);
            % 初始化生成长度为 "spf"的信号
            dataSrc = helperModClassGetSource(modulationTypes(mod_idx), spf, fs, sf); 
            % 初始化调制模块
            modulator = helperModClassGetModulator(modulationTypes(mod_idx), sps, fs, bw, fc);
          
            x = dataSrc();
            y = modulator(x);

            t = (0:length(y)-1) / fs; % 时间轴
            y = y .* exp(1j*2*pi*fc.*t.'); % 上变频
            frame = channel(y); % y为N * 1 % 过信道

            % 对该区域信号能量进行归一化
            % start_idx =  50 + randi([0 sps]);
            % frame_seg = frame(start_idx:end-start_idx+1,:);
            % framePower = mean(abs(frame_seg).^2);
            % frame_tx = frame_seg / sqrt(framePower);
            % 先裁剪信号为crop_length长度
            frame_seg = random_crop(frame, crop_length, sps, modsignal_len(idx));
            framePower = mean(abs(frame_seg).^2);
            frame_tx = frame_seg / sqrt(framePower);

            % 对信号进行信道化
            if channelization
                [ret] = IsValidNarrowBW(fc, 2.5e6, fs, nb_fc, nb_bw, nb_fs);
                if ret ~= 1 % 带宽参数无效
                    warning("无效带宽输入：%s\n");
                    continue;
                end
                [frame_crop] = ExtractNBSig(frame_tx, fc, 2.5e6, fs, nb_fc, nb_bw, nb_fs, crop_length);%信道化后数据
            else
                % 随机截取信号长度为crop_length的信号
                frame_crop = random_crop1(frame_tx, crop_length);
                % has_nan = any(isnan(frame_crop(:)))
                % frame_crop = frame_crop.* exp(-1j*2*pi*fc.*((1:length(frame_crop-1))/fs).'); % 下变频
            end
            
            % 存储I/Q两路数据
            real_frame_save = real(frame_crop.');
            imag_frame_save = imag(frame_crop.');
            data(sample_idx, 1, :) = real_frame_save;
            data(sample_idx, 2, :) = imag_frame_save;

            % 存储标签和SNR
            labels(sample_idx) = label_value;
            snr_values(sample_idx) = snr;
            sample_idx = sample_idx + 1;
            release(channel) % 如果经过信道的信号长度改变，则需要release()
        end 
    end
end
toc
%% 4. 存储数据集
% 4.1 判断数据集中是否存在nan值
has_nan = any(isnan(data(:)));  % 返回逻辑值 true/false

if has_nan
    error('数组 X 中包含 NaN 值');
else
    disp('数组 X 中不包含 NaN 值');

 % 4.2 创建HDF5文件
output_file = myconfig.output_file; % 存储路径设置
alldataset_file = fullfile(output_file, 'moddataset_sim.hdf5');
if exist(alldataset_file, 'file')
   delete(alldataset_file);
end
    
% 4.3 存储数据集
moddatasve_h5file(alldataset_file, data, labels, snr_values, mod_type_cell,fs, fc, snr_levels)
    
% 4.4 划分数据集
% 将数据集按照比例划分为训练集、验证集和测试集，并存为.h5文件
spiltset_saveh5(alldataset_file, train_ratio, val_ratio, test_ratio, output_file)
end
