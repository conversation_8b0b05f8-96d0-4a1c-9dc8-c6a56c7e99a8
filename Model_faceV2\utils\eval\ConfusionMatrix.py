import os
import torch.nn.functional as F
import sys
import numpy as np
import torch
import time
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix, classification_report, davies_bouldin_score
import getopt
from nets.arcface import Arcface
from usrlib.usrlib import *
from utils.dataloader import SigNbDataset, SigNbDataset_collate
from torch.utils.data import DataLoader

from utils.eval.tsnePlot import plot_features_pcaForClassify


class Analyzer:
    def __init__(self, model_path=None, clsdef_file=None, modeltype=0,task = None,file_test = None):
        self.modeltype = modeltype
        self.LenNorm = 512 * 46  # 应该<=24000 ; 6ms for 4M sps
        self.batch_size = 64
        self.task = task if task is not None else 'CM'
        self.clsdef_file = clsdef_file
        self.file_test = file_test
        self.model_path = model_path
        
        # 这些将在数据集加载后根据实际情况更新
        self.actual_cls_ids = None
        self.actual_cls_names = None
        self.actual_cls_count = None

        self._analyseInit()

    def _load_model(self, model_path):
        """加载预训练模型"""
        print("加载模型中...")
        model = Arcface(num_classes=self.cls_count, backbone="DroneSigNet", 
                       mode="predict", ModelType=self.modeltype)
        Init_model(model, True, model_path)
        model.eval()
        print("模型加载完成")
        return model
        
    def _get_actual_classes_from_data(self, y_true, y_pred):
        """
        根据测试数据中实际出现的类别ID，获取对应的类别名称
        Args:
            y_true: 真实标签
            y_pred: 预测标签
        Returns:
            actual_cls_ids: 实际出现的类别ID列表
            actual_cls_names: 实际出现的类别名称列表
        """
        # 获取实际出现的所有类别ID
        unique_labels = np.unique(np.concatenate([y_true, y_pred]))
        unique_labels = np.sort(unique_labels)  #这里一定要重新排序
        
        print(f"测试集中实际出现的类别ID: {unique_labels}")
        
        # 获取对应的类别名称
        actual_cls_names = []
        for label_id in unique_labels:
            if label_id < len(self.cls_names):
                actual_cls_names.append(self.cls_names[label_id])
            else:
                actual_cls_names.append(f"Unknown_{label_id}")
        
        print(f"对应的类别名称: {actual_cls_names}")
        
        # 更新实际类别信息
        self.actual_cls_ids = unique_labels
        self.actual_cls_names = actual_cls_names
        self.actual_cls_count = len(unique_labels)
        
        print(f"实际类别个数: {self.actual_cls_count}")
        # 每次更新后将self.cls_ids, self.cls_names 与unique_labels, actual_cls_names进行对比得到二者映射关系
        # 创建真实标签到数据集标签的映射关系
        self.label_mapping = {real_id: idx for idx, real_id in enumerate(unique_labels)}
        return unique_labels, actual_cls_names
        
    def load_dataset(self, dataset_file):
        """
        加载测试数据集
        Args:
            dataset_file: 数据集文件路径
        Returns:
            test_loader: 数据加载器
        """
        print(f'加载测试数据文件: {dataset_file}')
        
        with open(dataset_file, encoding='utf-8') as f:
            lines_test = f.readlines()
            
        # 数据打乱
        np.random.seed(10101)
        np.random.shuffle(lines_test)
        np.random.seed(None)
        
        # 创建数据集和数据加载器
        test_dataset = SigNbDataset(lines_test)
        test_loader = DataLoader(test_dataset, batch_size=self.batch_size, 
                                collate_fn=SigNbDataset_collate, drop_last=False)
        
        clsdef_dir, annotation_path_train, annotation_path_val, annotation_path_test, windows_path, linux_path, windows_path_local  =  read_path_config() #读取路径配置文件
        if sys.platform.startswith('win'):
            print("当前系统是Windows系统")
        if windows_path!=windows_path_local: # 判断windows路径和本地windows路径是否相同
            windows_to_local_path(windows_path, windows_path_local, lines_test)
        elif sys.platform.startswith('linux'):
            print("当前系统是Linux系统") #路径转换
            windows_to_linux_path(windows_path, linux_path, lines_test)
        else:
            print("当前系统不是Windows也不是Linux系统")  

        print(f'数据集大小: {len(test_dataset)}')
        
        # 检查数据集中的类别分布
        print("检查测试集中的类别分布...")
        class_counts = {}
        for line in lines_test:
            parts = line.strip().split()
            if len(parts) >= 2:
                class_id = int(parts[1])
                if class_id < len(self.cls_names):
                    class_name = self.cls_names[class_id]
                    class_counts[class_name] = class_counts.get(class_name, 0) + 1
        
        print("测试集类别分布:")
        for class_name, count in sorted(class_counts.items()):
            print(f"  {class_name}: {count} 样本")
        
        return test_loader
        
    def predict_classpro(self, test_loader):
        """
        对测试数据进行预测
        Args:
            test_loader: 测试数据加载器
        Returns:
            y_true: 真实标签
            y_pred: 预测标签
            y_prob: 预测概率
        """
        print("开始预测...")
        output_all = []
        classid_gt = []
        
        with torch.no_grad():
            for idx, (data, labels) in enumerate(test_loader):
                y = data.cuda()
                model_output = self.model(y)  #分类任务中返回是概率值
                output_all.append(model_output.softmax(dim=1))
                classid_gt.append(labels)
                
        # 合并所有批次的结果
        output_all = torch.cat(output_all, dim=0)
        classid_gt = torch.cat(classid_gt, dim=0)
        # 获取预测结果
        y_pred = torch.argmax(output_all, dim=1).cpu().numpy()
        y_true = classid_gt.cpu().numpy()
        y_prob = output_all.cpu().numpy()
        
        # 根据实际数据更新类别信息
        self._get_actual_classes_from_data(y_true, y_pred)
        # 计算准确率
        accuracy = np.mean(y_pred == y_true)
        print(f'整体准确率: {accuracy:.4f} ({accuracy*100:.2f}%)')

        return y_true, y_pred, y_prob


    def predict_feature(self, test_loader):
        """
        对测试数据进行预测
        # 设置model_type ==1
        Args:
            test_loader: 测试数据加载器
        Returns:
            y_true: 真实标签
            y_pred: 预测标签
            y_prob: 预测概率
        """
        print("开始预测...")
        output_all = []
        classid_gt = []

        with torch.no_grad():
            for idx, (data, labels) in enumerate(test_loader):
                y = data.cuda()
                model_output = self.model(y)  # 分类任务中返回是概率值,这里返回的是特征
                output_all.append(model_output)
                classid_gt.append(labels)

        # 合并所有批次的结果
        output_all = torch.cat(output_all, dim=0)


        predicted_labels = F.normalize(output_all, dim=1)  # arcface-loss这里要求是对归一化之后的特征进行处理
        predicted_labels = torch.argmax(self.model.ClsHead(predicted_labels).softmax(dim=1), dim=1).cpu().numpy()  # [batch self.nclass]

        classid_gt = torch.cat(classid_gt, dim=0)



        return output_all,classid_gt,predicted_labels
        
    def plot_confusion_matrix(self, y_true, y_pred, save_path='confusion_matrix.png', 
                            normalize=None, figsize=(12, 10)):
        """
        绘制混淆矩阵（仅包含实际出现的类别）
        Args:
            y_true: 真实标签
            y_pred: 预测标签
            save_path: 保存路径
            normalize: 归一化方式 ('true', 'pred', 'all', None)
            figsize: 图像大小
        """
        # 确保已经获取了实际类别信息
        if self.actual_cls_ids is None:
            self._get_actual_classes_from_data(y_true, y_pred)

        # 重新映射标签
        y_true_mapped = np.array([self.label_mapping[label] for label in y_true])
        y_pred_mapped = np.array([self.label_mapping[label] for label in y_pred])
        
        # 计算混淆矩阵（使用映射后的标签）
        cm = confusion_matrix(y_true_mapped, y_pred_mapped)
        
        print(f"混淆矩阵形状: {cm.shape} (实际类别数: {self.actual_cls_count})")
        print(f"总样本数: {cm.sum()}")
        print(f"对角线元素和: {np.trace(cm)}")
        print(f"整体准确率: {np.trace(cm) / cm.sum():.4f}")
        
        # 归一化处理
        if normalize == 'true':
            # 按行归一化，避免除零错误
            cm_sum = cm.sum(axis=1)[:, np.newaxis]
            cm_sum[cm_sum == 0] = 1  # 避免除零
            cm_normalized = cm.astype('float') / cm_sum
            title = f'Normalized Confusion Matrix (Row Normalize) - class num:{self.actual_cls_count}'
            fmt = '.0%'  # 使用百分比格式显示数字
            cbar_label = 'Percentage'
        elif normalize == 'pred':
            # 按列归一化
            cm_sum = cm.sum(axis=0)[np.newaxis, :]
            cm_sum[cm_sum == 0] = 1  # 避免除零
            cm_normalized = cm.astype('float') / cm_sum
            title = f'Normalized Confusion Matrix (Coloum Normalize) -class num:{self.actual_cls_count}'
            fmt = '.2f'
            cbar_label = 'Ratio'
        elif normalize == 'all':
            cm_normalized = cm.astype('float') / cm.sum()
            title = f'归一化混淆矩阵 (Global Normalize) - class num:{self.actual_cls_count}'
            fmt = '.4f'
            cbar_label = 'Ratio'
        else:
            cm_normalized = cm
            title = f'Normalized Confusion Matrix -class num:{self.actual_cls_count}'
            fmt = 'd'
            cbar_label = 'Count'
            
        # 绘制混淆矩阵
        plt.figure(figsize=figsize)
        
        # 根据是否归一化选择颜色映射
        if normalize is not None:
            vmax = 1.0 if normalize == 'true' else None
            sns.heatmap(cm_normalized, annot=True, fmt=fmt, cmap='Blues',
                       xticklabels=self.actual_cls_names, yticklabels=self.actual_cls_names,
                       cbar_kws={'label': cbar_label}, vmin=0, vmax=vmax)
        else:
            sns.heatmap(cm_normalized, annot=True, fmt=fmt, cmap='Blues',
                       xticklabels=self.actual_cls_names, yticklabels=self.actual_cls_names,
                       cbar_kws={'label': cbar_label})
        
        plt.title(title, fontsize=16)
        plt.xlabel('Predicted Label', fontsize=14)
        plt.ylabel('True Label', fontsize=14)
        plt.xticks(rotation=45, ha='right')
        plt.yticks(rotation=0)
        plt.tight_layout()
        
        # 保存图像
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f'混淆矩阵已保存至: {save_path}')
        plt.show()
        
        return cm


    def run_comfusionmatrix_analysis(self, save_dir='./results/'):
        """
        运行完整的混淆矩阵分析（基于实际出现的类别）
        Args:
            save_dir: 结果保存目录
        """
        # 创建保存目录
        os.makedirs(save_dir, exist_ok=True)
        
        print("开始混淆矩阵分析...")
        start_time = time.time()
        
        # 1. 进行预测
        y_true, y_pred, y_prob = self.predict_classpro(self.test_loder)
        
        # 2. 绘制混淆矩阵
        cm_path = os.path.join(save_dir, 'confusion_matrix.png')
        cm_norm_path = os.path.join(save_dir, 'confusion_matrix_normalized.png')

        # 原始混淆矩阵
        print("\n绘制原始混淆矩阵...")
        cm = self.plot_confusion_matrix(y_true, y_pred, save_path=cm_path)
        
        # 归一化混淆矩阵
        print("\n绘制归一化混淆矩阵...")
        cm_norm = self.plot_confusion_matrix(y_true, y_pred, save_path=cm_norm_path, 
                                           normalize='true')

        end_time = time.time()
        print(f"\n分析完成! 总耗时: {end_time - start_time:.2f}秒")
        print(f"结果保存在: {save_dir}")
        
        # 输出关键结果摘要
        print(f"\n🔍 分析摘要:")
        print(f"📊 实际分析类别数: {self.actual_cls_count} (总定义类别: {self.cls_count})")
        print(f"📋 实际出现的类别: {', '.join(self.actual_cls_names)}")

    def run_plotpca(self, save_dir='./results/'):
        # 创建保存目录
        os.makedirs(save_dir, exist_ok=True)

        print("开始PCA分析...")
        predicted_features, true_labels,y_pred= self.predict_feature(self.test_loder)
        self._get_actual_classes_from_data(true_labels, y_pred)
        true_labels = [self.label_mapping[i.item()] for i in true_labels]

        # note：根据当前的测试需求选择指定的函数
        dbi = davies_bouldin_score(np.array(predicted_features.cpu()), true_labels)
        print('=' * 60)
        print(f'DBI is:{dbi}')
        print('='*60)
        plot_features_pcaForClassify(predicted_features, true_labels, class_names=self.actual_cls_names)  # 绘制指定的

    def _analyseInit(self):
        # 1. 读取模型训练时的类别定义
        self.cls_ids, self.cls_names, self.cls_count = get_classes(self.clsdef_file)
        print(f'类别定义文件中的类别个数: {self.cls_count}')
        # 2. 加载模型
        self.model = self._load_model(self.model_path)
        # 3. 加载指定数据集
        self.test_loder =  self.load_dataset(self.file_test)
