#!/usr/bin/env python3
"""
信道扫描分段查看工具核心功能模块

包含文件操作、信道扫描、分段管理等核心功能
"""

import os
import sys
import numpy as np
import threading
import time
from pathlib import Path
import torch
import re
import logging
from datetime import datetime

# 添加项目路径
parent_dir = Path(__file__).parent.parent
sys.path.append(str(parent_dir))

from chanlib.func_scansig import proc_wbsig
from usrlib.usrlib import Read_sigfile, AddArcVector, ViewArcVectorDB, GetArcVectorDB, compute_stft, Init_model
from usrlib.MyHelper import read_signal_from_hdf5
from utils.dataloader import LoadHdfsDataset
from nets.arcface import Arcface


def setup_logging():
    """
    设置日志记录配置
    """
    try:
        # 创建logs目录
        log_dir = os.path.join(os.path.dirname(__file__), 'logs')
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)

        # 配置日志格式
        log_format = '%(asctime)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'

        # 创建日志文件名（按日期）
        log_filename = os.path.join(log_dir, f'chanscan_viewer_{datetime.now().strftime("%Y%m%d")}.log')

        # 配置日志记录器
        logging.basicConfig(
            level=logging.INFO,
            format=log_format,
            handlers=[
                logging.FileHandler(log_filename, encoding='utf-8'),
                logging.StreamHandler()  # 同时输出到控制台
            ]
        )

        # 获取logger
        logger = logging.getLogger('chanscan_viewer')
        logger.info("日志系统初始化完成")
        return logger

    except Exception as e:
        print(f"设置日志记录失败: {e}")
        return logging.getLogger('chanscan_viewer')


def get_logger():
    """
    获取日志记录器
    """
    return logging.getLogger('chanscan_viewer')


def extract_signal_from_record(record):
    """
    从数据库记录中提取完整文件的信号数据和分段信息

    Args:
        record: 数据库记录元组 (class_id, class_name, file_path, sample_rate, bandwidth)

    Returns:
        tuple: (success, full_signal_data, segment_info, metadata, error_msg)
            - success (bool): 是否成功提取信号
            - full_signal_data (np.array): 完整文件的信号数据（复数格式）
            - segment_info (dict): 分段信息（起止位置等）
            - metadata (dict): 信号元数据
            - error_msg (str): 错误信息，成功时为空字符串
    """
    try:
        # 检查输入参数
        if not record or len(record) < 5:
            return False, None, None, None, "无效的数据库记录格式"

        # 兼容新旧格式的数据库记录
        # 新格式：(record_id, class_id, class_name, file_path, sample_rate, bandwidth, start_sample, end_sample)
        # 旧格式：(class_id, class_name, file_path, sample_rate, bandwidth)
        if len(record) >= 8:
            # 新格式（包含record_id和分段位置）
            record_id = record[0]
            class_id = record[1]
            class_name = record[2]
            file_path = record[3]
            sample_rate_str = record[4]
            bandwidth_str = record[5]
            start_sample = record[6]
            end_sample = record[7]
        elif len(record) >= 5:
            # 旧格式（不包含record_id和分段位置）
            class_id = record[0]
            class_name = record[1]
            file_path = record[2]
            sample_rate_str = record[3]
            bandwidth_str = record[4]
            start_sample = 0
            end_sample = 0
        else:
            return False, None, None, None, f"无效的数据库记录格式，记录长度: {len(record)}"

        # 清理文件路径
        clean_file_path = clean_path_string(file_path)

        # 调试信息（可选，用于问题诊断）
        # print(f"调试信息 - 数据库记录长度: {len(record)}")
        # print(f"调试信息 - 完整记录: {record}")
        # print(f"调试信息 - 类别ID: {repr(class_id)}")
        # print(f"调试信息 - 类别名称: {repr(class_name)}")
        # print(f"调试信息 - 原始文件路径: {repr(file_path)}")
        # print(f"调试信息 - 清理后路径: {repr(clean_file_path)}")
        # print(f"调试信息 - 采样率: {repr(sample_rate_str)}")
        # print(f"调试信息 - 带宽: {repr(bandwidth_str)}")

        # 检查文件是否存在
        if not os.path.exists(clean_file_path):
            # 提供更详细的错误信息
            error_details = (
                f"文件不存在: {clean_file_path}\n"
                f"原始路径: {file_path}\n"
                f"类别名称: {class_name}\n"
                f"数据库记录长度: {len(record)}\n"
                f"完整记录: {record}"
            )
            return False, None, None, None, error_details

        # 解析采样率和带宽
        try:
            # 从字符串中提取数值（去掉单位）
            sample_rate = float(sample_rate_str.replace(' MHz', '').replace('MHz', '')) * 1e6  # 转换为Hz
            bandwidth = float(bandwidth_str.replace(' MHz', '').replace('MHz', '')) * 1e6  # 转换为Hz
        except (ValueError, AttributeError):
            return False, None, None, None, f"无法解析采样率或带宽: {sample_rate_str}, {bandwidth_str}"

        # 根据文件扩展名选择读取方法
        file_ext = os.path.splitext(clean_file_path)[1].lower()

        if file_ext in ['.bvsp', '.dat']:
            # 使用Read_sigfile函数读取
            try:
                # 导入Read_sigfile函数
                import sys
                parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                if parent_dir not in sys.path:
                    sys.path.append(parent_dir)

                from usrlib.usrlib import Read_sigfile
                rxDatas, fs, fc, bw = Read_sigfile(clean_file_path, [0, -1])
                # 转换为复数信号
                full_signal_data = rxDatas[0, :, 0] + 1j * rxDatas[0, :, 1]

                # 尝试获取分段信息（从数据库记录推断）
                segment_info = get_segment_info_from_database_record(record, len(full_signal_data), float(fs))

                # 创建元数据
                metadata = {
                    'fs': float(fs),
                    'fc': float(fc),
                    'bw': float(bw),
                    'file_path': clean_file_path,
                    'class_id': class_id,
                    'class_name': clean_class_name(class_name),
                    'file_format': file_ext,
                    'total_length': len(full_signal_data)
                }

                return True, full_signal_data, segment_info, metadata, ""

            except Exception as e:
                return False, None, None, None, f"读取.bvsp/.dat文件失败: {str(e)}"

        elif file_ext == '.hdfv':
            # 使用read_signal_from_hdf5函数读取
            try:
                # 导入read_signal_from_hdf5函数
                import sys
                parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                if parent_dir not in sys.path:
                    sys.path.append(parent_dir)

                from usrlib.usrlib import read_signal_from_hdf5
                full_signal_data, hdf_metadata = read_signal_from_hdf5(clean_file_path)

                # 尝试获取分段信息
                segment_info = get_segment_info_from_database_record(record, len(full_signal_data), float(hdf_metadata.get('fs', sample_rate)))

                # 创建元数据
                metadata = {
                    'fs': float(hdf_metadata.get('fs', sample_rate)),
                    'fc': float(hdf_metadata.get('fc', 0)),
                    'bw': float(hdf_metadata.get('bw', bandwidth)),
                    'file_path': clean_file_path,
                    'class_id': class_id,
                    'class_name': clean_class_name(class_name),
                    'file_format': file_ext,
                    'timestamp': hdf_metadata.get('timestamp', ''),
                    'total_length': len(full_signal_data)
                }

                return True, full_signal_data, segment_info, metadata, ""

            except Exception as e:
                return False, None, None, None, f"读取.hdfv文件失败: {str(e)}"

        else:
            return False, None, None, None, f"不支持的文件格式: {file_ext}"

    except Exception as e:
        return False, None, None, None, f"提取信号数据时发生异常: {str(e)}"


def get_segment_info_from_database_record(record, total_signal_length, fs):
    """
    从数据库记录中获取分段信息（支持新的分段位置字段）

    Args:
        record: 数据库记录元组 (class_id, class_name, file_path, sample_rate_str, bandwidth_str, start_sample, end_sample)
        total_signal_length: 完整信号长度
        fs: 采样率

    Returns:
        dict: 分段信息，如果无法获取则返回默认值
    """
    try:
        # 检查记录格式（新版本包含分段位置）
        if len(record) >= 7:
            class_id, class_name, file_path, sample_rate_str, bandwidth_str, start_sample, end_sample = record[:7]

            # 使用数据库中存储的真实分段位置
            if start_sample > 0 or end_sample > 0:
                # 验证分段位置的有效性
                start_sample = max(0, min(start_sample, total_signal_length - 1))
                end_sample = max(start_sample + 1, min(end_sample, total_signal_length))

                segment_info = {
                    'start_sample': start_sample,
                    'end_sample': end_sample,
                    'start_time': start_sample / fs,
                    'end_time': end_sample / fs,
                    'duration': (end_sample - start_sample) / fs,
                    'class_id': class_id,
                    'class_name': clean_class_name(class_name),
                    'has_segment': True,  # 标记有真实分段信息
                    'center_freq': 0,  # 需要从文件中读取
                    'bandwidth': float(bandwidth_str.replace(' MHz', '').replace('MHz', '')) * 1e6
                }

                print(f"使用数据库中的分段位置: {start_sample} - {end_sample}")
                return segment_info

        # 旧版本记录或无效分段位置，使用默认方法
        class_id, class_name, file_path, sample_rate_str, bandwidth_str = record[:5]

        # 默认分段信息（假设是文件的中间部分）
        segment_length = min(int(0.1 * total_signal_length), 100000)  # 10%的长度或最多10万点
        segment_start = max(0, (total_signal_length - segment_length) // 2)  # 从中间开始
        segment_end = min(total_signal_length, segment_start + segment_length)

        segment_info = {
            'start_sample': segment_start,
            'end_sample': segment_end,
            'start_time': segment_start / fs,
            'end_time': segment_end / fs,
            'duration': (segment_end - segment_start) / fs,
            'class_id': class_id,
            'class_name': clean_class_name(class_name),
            'has_segment': False,  # 标记为估算的分段信息
            'center_freq': 0,
            'bandwidth': float(bandwidth_str.replace(' MHz', '').replace('MHz', '')) * 1e6
        }

        print(f"使用默认分段位置: {segment_start} - {segment_end}")
        return segment_info

    except Exception as e:
        print(f"获取分段信息失败: {str(e)}")
        # 返回默认分段信息
        return {
            'start_sample': 0,
            'end_sample': min(total_signal_length, 10000),
            'start_time': 0,
            'end_time': min(total_signal_length, 10000) / fs,
            'duration': min(total_signal_length, 10000) / fs,
            'class_id': record[0] if len(record) > 0 else 0,
            'class_name': 'Unknown',
            'has_segment': False,  # 标记无分段信息
            'center_freq': 0,
            'bandwidth': 0
        }


def estimate_segment_info_from_record(record, signal_length=None):
    """
    根据数据库记录推断分段信息

    Args:
        record: 数据库记录元组
        signal_length: 信号长度（可选）

    Returns:
        dict: 分段信息字典
    """
    try:
        class_id, class_name, file_path, sample_rate_str, bandwidth_str = record

        # 解析采样率和带宽
        sample_rate = float(sample_rate_str.replace(' MHz', '').replace('MHz', '')) * 1e6
        bandwidth = float(bandwidth_str.replace(' MHz', '').replace('MHz', '')) * 1e6

        # 推断分段信息
        segment_info = {
            'class_id': class_id,
            'class_name': clean_class_name(class_name),
            'sample_rate': sample_rate,
            'bandwidth': bandwidth,
            'center_frequency': 0,  # 默认值，需要从文件中读取
            'time_start': 0,  # 默认从开始
            'time_end': None,  # 默认到结束
            'frequency_start': -bandwidth/2,  # 相对于中心频率
            'frequency_end': bandwidth/2,    # 相对于中心频率
        }

        # 如果有信号长度，计算时间范围
        if signal_length is not None:
            segment_info['time_end'] = signal_length / sample_rate
            segment_info['duration_ms'] = (signal_length / sample_rate) * 1000

        return segment_info

    except Exception as e:
        print(f"推断分段信息失败: {str(e)}")
        return {}


def validate_and_preprocess_signal(signal_data, metadata):
    """
    验证和预处理信号数据

    Args:
        signal_data: 原始信号数据
        metadata: 信号元数据

    Returns:
        tuple: (success, processed_signal, processed_metadata, error_msg)
    """
    try:
        if signal_data is None or len(signal_data) == 0:
            return False, None, None, "信号数据为空"

        # 确保信号是复数格式
        if not np.iscomplexobj(signal_data):
            return False, None, None, "信号数据不是复数格式"

        # 检查信号长度
        signal_length = len(signal_data)
        if signal_length < 1000:
            return False, None, None, f"信号长度太短: {signal_length} 点"

        # 检查元数据完整性
        required_fields = ['fs', 'fc', 'bw', 'class_name']
        for field in required_fields:
            if field not in metadata:
                return False, None, None, f"缺少必要的元数据字段: {field}"

        # 处理信号数据（如果需要抽样）
        processed_signal = signal_data
        max_display_points = 1000000  # 最大显示点数

        if signal_length > max_display_points:
            # 抽样处理
            step = signal_length // max_display_points
            processed_signal = signal_data[::step]
            print(f"信号抽样: {signal_length} -> {len(processed_signal)} 点")

        # 更新元数据
        processed_metadata = metadata.copy()
        processed_metadata['original_length'] = signal_length
        processed_metadata['processed_length'] = len(processed_signal)
        processed_metadata['is_downsampled'] = signal_length > max_display_points

        if processed_metadata['is_downsampled']:
            processed_metadata['downsample_factor'] = step

        return True, processed_signal, processed_metadata, ""

    except Exception as e:
        return False, None, None, f"验证和预处理信号失败: {str(e)}"


def extract_signal_segment(signal_data, metadata, start_time=None, end_time=None, start_freq=None, end_freq=None):
    """
    从信号中提取特定时间和频率范围的片段

    Args:
        signal_data: 信号数据
        metadata: 信号元数据
        start_time: 开始时间（秒）
        end_time: 结束时间（秒）
        start_freq: 开始频率（Hz，相对于中心频率）
        end_freq: 结束频率（Hz，相对于中心频率）

    Returns:
        tuple: (success, segment_data, segment_metadata, error_msg)
    """
    try:
        fs = metadata['fs']
        signal_length = len(signal_data)

        # 时间范围处理
        if start_time is None:
            start_sample = 0
        else:
            start_sample = int(start_time * fs)
            start_sample = max(0, start_sample)

        if end_time is None:
            end_sample = signal_length
        else:
            end_sample = int(end_time * fs)
            end_sample = min(signal_length, end_sample)

        if start_sample >= end_sample:
            return False, None, None, "无效的时间范围"

        # 提取时间片段
        segment_data = signal_data[start_sample:end_sample]

        # 频率滤波（如果指定了频率范围）
        if start_freq is not None or end_freq is not None:
            # 这里可以添加频域滤波逻辑
            # 暂时跳过频率滤波，直接使用时间片段
            pass

        # 创建片段元数据
        segment_metadata = metadata.copy()
        segment_metadata['segment_start_time'] = start_sample / fs
        segment_metadata['segment_end_time'] = end_sample / fs
        segment_metadata['segment_duration'] = (end_sample - start_sample) / fs
        segment_metadata['segment_length'] = len(segment_data)
        segment_metadata['start_sample'] = start_sample
        segment_metadata['end_sample'] = end_sample

        return True, segment_data, segment_metadata, ""

    except Exception as e:
        return False, None, None, f"提取信号片段失败: {str(e)}"


def clean_path_string(path_str):
    """
    清理路径字符串，移除字节字符串标记和多余的引号

    Args:
        path_str: 可能包含b'...'格式的路径字符串

    Returns:
        str: 清理后的纯字符串路径
    """
    if path_str is None:
        return ""

    # 转换为字符串
    if isinstance(path_str, bytes):
        path_str = path_str.decode('utf-8')
    else:
        path_str = str(path_str)

    # 移除字节字符串标记 b'...' 或 b"..."
    path_str = re.sub(r"^b['\"](.*)['\"]\s*$", r'\1', path_str)

    # 移除多余的引号
    path_str = path_str.strip('\'"')

    # 标准化路径分隔符
    path_str = path_str.replace('\\', '/')

    return path_str


def clean_class_name(class_name):
    """
    清理类名字符串，移除字节字符串标记和多余的引号

    Args:
        class_name: 可能包含b'...'格式的类名字符串

    Returns:
        str: 清理后的纯字符串类名
    """
    if class_name is None:
        return ""

    # 转换为字符串
    if isinstance(class_name, bytes):
        class_name = class_name.decode('utf-8')
    else:
        class_name = str(class_name)

    # 移除字节字符串标记 b'...' 或 b"..."
    class_name = re.sub(r"^b['\"](.*)['\"]\s*$", r'\1', class_name)

    # 移除多余的引号
    class_name = class_name.strip('\'"')

    return class_name


def load_arcface_model(model_path, num_classes=120):
    """
    加载预训练的模型
    
    Args:
        model_path (str): 模型权重文件路径
        num_classes (int): 模型分类数
        
    Returns:
        torch.nn.Module: 加载好的模型
    """
    try:
        model = Arcface(num_classes=num_classes, backbone="DroneSigNet", mode="predict", ModelType=1)
        
        # 验证传入的模型路径是否存在
        if not os.path.exists(model_path):
             raise FileNotFoundError(f"指定的模型文件路径不存在: {model_path}")

        print(f"正在从 {model_path} 加载模型...")
        Init_model(model, True, model_path)
        model.eval()
        return model
    except Exception as e:
        print(f"加载模型失败: {e}")
        return None


def load_signal_data(file_path):
    """
    加载信号数据文件
    
    Args:
        file_path (str): 文件路径
        
    Returns:
        tuple: (success, signal_data, metadata, error_msg)
    """
    try:
        file_ext = os.path.splitext(file_path)[1].lower()
        
        if file_ext in ['.bvsp', '.dat']:
            # 使用 Read_sigfile 读取 .bvsp/.dat 格式
            result = Read_sigfile(file_path)
            if result is None:
                return False, None, None, "文件格式不正确或读取失败"
            
            sigdata, samp_rate, center_freq, bandwidth = result
            
            # 转换为复数信号
            if sigdata.shape[-1] == 2:  # I/Q 格式
                signal = sigdata[0, :, 0] + 1j * sigdata[0, :, 1]
            else:
                signal = sigdata[0, :]
            
            metadata = {
                'fs': float(samp_rate),
                'fc': float(center_freq),
                'bw': float(bandwidth),
                'file_size': os.path.getsize(file_path),
                'signal_length': len(signal),
                'duration': len(signal) / float(samp_rate)
            }
            
            return True, signal, metadata, ""
            
        elif file_ext == '.hdfv':
            # 使用 read_signal_from_hdf5 读取 HDF5 格式
            try:
                # 正确解包 read_signal_from_hdf5 返回的元组 (signal, metadata)
                signal_data, hdf_metadata = read_signal_from_hdf5(file_path)

                # 从HDF5文件的元数据中提取信息（与data_viewer保持一致）
                samp_rate = hdf_metadata.get('fs', 61.44e6)
                center_freq = hdf_metadata.get('fc', 915e6)
                bandwidth = hdf_metadata.get('bw', 50e6)

                # 构建标准化的元数据格式
                metadata = {
                    'fs': float(samp_rate),
                    'fc': float(center_freq),
                    'bw': float(bandwidth),
                    'file_size': os.path.getsize(file_path),
                    'signal_length': len(signal_data),
                    'duration': len(signal_data) / float(samp_rate)
                }

                return True, signal_data, metadata, ""

            except Exception as e:
                return False, None, None, f"HDF5文件读取失败: {str(e)}"
        else:
            return False, None, None, f"不支持的文件格式: {file_ext}"
            
    except Exception as e:
        return False, None, None, f"加载文件失败: {str(e)}"


def run_channel_scan(signal_data, metadata, progress_callback=None, original_file_path=None):
    """
    运行信道扫描分段

    Args:
        signal_data: 信号数据
        metadata: 元数据字典
        progress_callback: 进度回调函数
        original_file_path: 原始文件路径（可选）

    Returns:
        tuple: (success, segments, fs_val_scalar, error_msg)
    """
    try:
        if progress_callback:
            progress_callback("开始信道扫描...")
        
        # 获取参数
        wb_fs = metadata.get('fs', 15.36e6)
        wb_fc = metadata.get('fc', 915e6)
        wb_bw = metadata.get('bw', 12e6)
        
        # 运行 proc_wbsig 进行信道扫描
        print(f"正在处理信号... 长度: {len(signal_data)}, fs: {wb_fs/1e6:.1f}MHz, fc: {wb_fc/1e6:.1f}MHz, bw: {wb_bw/1e6:.1f}MHz")
        
        if progress_callback:
            progress_callback("正在进行信号处理...")
        
        proc_wbsig(signal_data, wb_fs, wb_fc, wb_bw)
        
        # 检查生成的数据集文件
        dataset_name = "fchanscan-S1.hdf5"
        if not os.path.exists(dataset_name):
            return False, [], None, "信号处理未生成有效的数据集文件"
        
        if progress_callback:
            progress_callback("正在读取分段结果...")
        
        # 读取分段结果
        try:
            rx_signal, classid_gt, class_name, fs_value, bw_value, fc_values, start_poses, end_poses, snr_values, duration_values = LoadHdfsDataset(dataset_name)
            
            # 提取标量采样率，用于后续计算
            fs_val_scalar = fs_value
            if hasattr(fs_val_scalar, '__len__') and len(fs_val_scalar) > 0:
                fs_val_scalar = fs_val_scalar[0]
            if hasattr(fs_val_scalar, 'item'):
                fs_val_scalar = fs_val_scalar.item()

            # 构建分段信息列表
            segments = []
            num_segments = len(start_poses) if hasattr(start_poses, '__len__') else 0
            
            for i in range(num_segments):
                # 安全获取各个值
                start_pos = start_poses[i] if i < len(start_poses) else 0
                end_pos = end_poses[i] if i < len(end_poses) else 0
                fc_val = fc_values[i] if i < len(fc_values) else wb_fc
                snr_val = snr_values[i] if i < len(snr_values) else 0
                duration_val = duration_values[i] if i < len(duration_values) else 0
                
                # 处理可能的数组格式
                if hasattr(start_pos, '__len__') and len(start_pos) > 0:
                    start_pos = start_pos[0]
                if hasattr(end_pos, '__len__') and len(end_pos) > 0:
                    end_pos = end_pos[0]
                if hasattr(fc_val, '__len__') and len(fc_val) > 0:
                    fc_val = fc_val[0]
                if hasattr(snr_val, '__len__') and len(snr_val) > 0:
                    snr_val = snr_val[0]
                if hasattr(duration_val, '__len__') and len(duration_val) > 0:
                    duration_val = duration_val[0]
                
                # 转换为Python原生类型
                if hasattr(start_pos, 'item'):
                    start_pos = start_pos.item()
                if hasattr(end_pos, 'item'):
                    end_pos = end_pos.item()
                if hasattr(fc_val, 'item'):
                    fc_val = fc_val.item()
                if hasattr(snr_val, 'item'):
                    snr_val = snr_val.item()
                if hasattr(duration_val, 'item'):
                    duration_val = duration_val.item()
                
                # 处理类别ID和名称
                class_id_val = classid_gt[i] if i < len(classid_gt) else 999
                class_name_raw = class_name[i][0] if i < len(class_name) and len(class_name[i]) > 0 else b'unknown'
                class_name_str = class_name_raw.decode('utf-8')
                if hasattr(class_id_val, 'item'):
                    class_id_val = class_id_val.item()

                # 计算带宽
                bw_val = bw_value
                if hasattr(bw_val, '__len__') and len(bw_val) > 0:
                    bw_val = bw_val[0]
                if hasattr(bw_val, 'item'):
                    bw_val = bw_val.item()
                
                segment = {
                    'id': i,
                    'start_pos': int(start_pos),
                    'end_pos': int(end_pos),
                    'center_freq': float(fc_val),
                    'bandwidth': float(bw_val),
                    'duration': float(duration_val),
                    'snr': float(snr_val),
                    'signal_data': rx_signal[i] if i < len(rx_signal) else None,
                    'fs': float(fs_val_scalar),
                    'class_id': int(class_id_val),
                    'class_name': class_name_str,
                    'original_file_path': original_file_path  # 添加原始文件路径
                    # TODO类别名称和类别id的获取方式不正确，chanel scaner不会给出id和名称，之前能获取的方法是hdfv是以及带好标记的数据，bvsp文件不带有分类标记。
                }
                
                segments.append(segment)
            
            if progress_callback:
                progress_callback(f"扫描完成，检测到 {len(segments)} 个信号段")
            
            # 清理临时文件
            if os.path.exists(dataset_name):
                os.remove(dataset_name)
            
            return True, segments, float(fs_val_scalar), ""
            
        except Exception as e:
            return False, [], None, f"读取分段结果失败: {str(e)}"
            
    except Exception as e:
        return False, [], None, f"信道扫描失败: {str(e)}"


def get_next_record_id():
    """
    获取数据库中下一个可用的record_id（最后一条记录的record_id + 1）
    用于数据编号

    Returns:
        int: 下一个可用的record_id
    """
    try:
        from usrlib.usrlib import get_next_record_id as get_next_id
        return get_next_id()
    except Exception as e:
        print(f"获取下一个record_id失败: {e}")
        return 1


def get_class_id_for_segments(segments, folder_name=None):
    """
    为分段获取合适的class_id（类别标识符）
    基于class_def.txt文件中的类别定义

    Args:
        segments: 分段列表
        folder_name: 文件夹名称（可选）

    Returns:
        int: 类别ID
    """
    try:
        # 导入infer_class_id_from_path函数
        import sys
        import os
        current_dir = os.path.dirname(os.path.abspath(__file__))
        parent_dir = os.path.dirname(current_dir)
        if parent_dir not in sys.path:
            sys.path.append(parent_dir)

        from usrlib.usrlib import infer_class_id_from_path

        # 构造一个虚拟路径用于推断class_id
        if folder_name:
            virtual_path = f"DataFiles/{folder_name}/dummy_file.dat"
        else:
            virtual_path = "DataFiles/unknown/dummy_file.dat"

        # 使用统一的推断逻辑
        class_id = infer_class_id_from_path(virtual_path)
        return class_id

    except Exception as e:
        print(f"获取class_id失败: {e}")
        # 抛出异常而不是返回默认值
        raise Exception(f"无法获取正确的class_id: {e}")


def get_next_class_id():
    """
    保持向后兼容性的函数，现在返回record_id

    Returns:
        int: 下一个可用的record_id（为了向后兼容）
    """
    return get_next_record_id()


def get_folder_name_from_segments(segments):
    """
    从分段信息中获取文件夹名字

    Args:
        segments: 分段列表

    Returns:
        str: 文件夹名字
    """
    try:
        # 尝试从第一个分段的元数据中获取原始文件路径
        if segments and len(segments) > 0:
            first_segment = segments[0]

            # 检查是否有原始文件路径信息
            if 'original_file_path' in first_segment:
                file_path = first_segment['original_file_path']
                folder_name = os.path.basename(os.path.dirname(file_path))
                if folder_name:
                    return folder_name

            # 如果没有原始文件路径，尝试从class_name中提取
            raw_class_name = first_segment.get('class_name', '')
            if raw_class_name and raw_class_name != 'unknown':
                # 根据'\'或'/'分割路径，获取第一部分作为文件夹名
                folder_name = raw_class_name.replace('\\', '/').split('/')[0]
                if folder_name:
                    return folder_name

        # 默认返回
        return 'chanscan_segments'

    except Exception as e:
        print(f"获取文件夹名字失败: {e}")
        return 'chanscan_segments'


def add_segments_to_db(model, segments, original_file_path=None):
    """
    使用模型提取选中分段的特征并批量添加到数据库

    Args:
        model: 加载好的模型
        segments: 选中的分段列表
        original_file_path: 原始文件路径（可选）

    Returns:
        tuple: (success, added_count, error_msg)
    """
    if model is None:
        return False, 0, "模型未加载"

    if not segments:
        return False, 0, "没有选中的分段"

    try:
        added_count = 0
        errors = []

        # 获取文件夹名字作为class_name
        if original_file_path:
            folder_name = os.path.basename(os.path.dirname(original_file_path))
            if not folder_name:
                folder_name = get_folder_name_from_segments(segments)
        else:
            folder_name = get_folder_name_from_segments(segments)

        print(f"使用文件夹名字作为class_name: {folder_name}")

        # 获取类别ID（基于文件夹名称）
        class_id = get_class_id_for_segments(segments, folder_name)
        print(f"使用class_id: {class_id} (基于文件夹名称: {folder_name})")

        # 复制源文件到DataFiles文件夹（如果提供了原始文件路径）
        copied_file_path = None
        if original_file_path and os.path.exists(original_file_path):
            try:
                # 导入文件复制函数
                import sys
                parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                if parent_dir not in sys.path:
                    sys.path.append(parent_dir)

                from usrlib.MyHelper import copy_source_file_to_datafiles

                # 执行文件复制
                copy_success, copied_file_path, copy_error = copy_source_file_to_datafiles(
                    original_file_path, folder_name
                )

                if copy_success:
                    print(f"✓ 源文件已复制到: {copied_file_path}")
                else:
                    print(f"⚠ 源文件复制失败: {copy_error}")
                    print("  数据库添加将继续进行...")

            except Exception as e:
                print(f"⚠ 文件复制过程中发生错误: {str(e)}")
                print("  数据库添加将继续进行...")
        elif original_file_path:
            print(f"⚠ 源文件不存在，跳过复制: {original_file_path}")
        else:
            print("ℹ 未提供源文件路径，跳过文件复制")

        print(f"开始处理 {len(segments)} 个分段...")

        for i, segment in enumerate(segments):
            try:
                # 获取分段的信号数据
                segment_signal_data = segment.get('signal_data')
                if segment_signal_data is None:
                    errors.append(f"分段 {i+1}: 没有有效的信号数据")
                    continue

                print(f"处理分段 {i+1}/{len(segments)}, 信号形状: {segment_signal_data.shape}")

                # 准备模型输入 - 处理复数信号数据
                if np.iscomplexobj(segment_signal_data):
                    # 如果是复数数组，分离I和Q分量
                    if segment_signal_data.ndim == 1:
                        # 一维复数数组 [N] -> [N, 2]
                        i_component = np.real(segment_signal_data)
                        q_component = np.imag(segment_signal_data)
                        segment_signal_data = np.column_stack([i_component, q_component])
                    else:
                        raise ValueError(f"不支持的复数信号维度: {segment_signal_data.shape}")
                else:
                    # 如果是实数数组，检查维度
                    if segment_signal_data.ndim == 2:
                        if segment_signal_data.shape[0] == 2:  # [2, N] -> [N, 2]
                            segment_signal_data = segment_signal_data.T
                        elif segment_signal_data.shape[1] != 2:  # 不是 [N, 2] 格式
                            raise ValueError(f"实数信号数据应为 [N, 2] 或 [2, N] 格式，实际: {segment_signal_data.shape}")
                    else:
                        raise ValueError(f"不支持的实数信号维度: {segment_signal_data.shape}")

                print(f"转换后信号形状: {segment_signal_data.shape}")

                # 确保数据是连续的并转换为float32
                segment_signal_data = np.ascontiguousarray(segment_signal_data.astype(np.float32))

                # 验证信号长度是否符合模型期望
                from utils.dataloader import getLenNorm
                expected_length = getLenNorm()  # 23808 个采样点
                current_length = segment_signal_data.shape[0]
                if current_length != expected_length:
                    print(f"警告: 信号长度 {current_length} 与期望长度 {expected_length} 不匹配")

                sig_tensor = torch.from_numpy(segment_signal_data).unsqueeze(0)
                sig_tensor = sig_tensor.cuda()

                # 模型推理
                with torch.no_grad():
                    vec = model(sig_tensor)
                    vec = vec.squeeze()

                # 获取特征向量
                feature_vector = vec.detach().cpu().numpy()

                # 使用统一的class_id和文件夹名字作为class_name
                current_class_id = class_id  # 所有分段使用相同的class_id（类别标识符）
                class_name = folder_name

                # 构建文件路径信息 - 优先使用备份文件的相对路径
                if copied_file_path:
                    # 如果文件复制成功，使用备份文件的相对路径
                    # 将绝对路径转换为相对于项目根目录的路径
                    if os.path.isabs(copied_file_path):
                        # 获取相对路径：从当前工作目录开始的相对路径
                        try:
                            file_path = os.path.relpath(copied_file_path)
                            # 统一使用正斜杠
                            file_path = file_path.replace('\\', '/')
                        except ValueError:
                            # 如果无法计算相对路径，使用DataFiles开始的路径
                            if 'DataFiles' in copied_file_path:
                                file_path = copied_file_path[copied_file_path.find('DataFiles'):]
                                file_path = file_path.replace('\\', '/')
                            else:
                                file_path = copied_file_path
                    else:
                        file_path = copied_file_path.replace('\\', '/')
                elif original_file_path:
                    # 如果文件复制失败但有原始路径，使用原始路径
                    file_path = f"{original_file_path}"
                else:
                    # 如果都没有，使用文件夹名
                    file_path = f"{folder_name}"

                # 清理路径字符串，确保是纯字符串格式
                file_path = clean_path_string(file_path)

                # 清理类名字符串，确保是纯字符串格式
                clean_class_name_str = clean_class_name(class_name)

                # 从分段自身获取元数据
                fs_val = segment.get('fs', 0.0)
                bw_val = segment.get('bandwidth', 0.0)

                # 获取分段位置信息
                start_sample = segment.get('start_pos', 0)
                end_sample = segment.get('end_pos', 0)

                # 添加到数据库（包含分段位置）
                AddArcVector(
                    feature_vector,
                    current_class_id,
                    clean_class_name_str,
                    file_path,
                    fs_val,
                    bw_val,
                    start_sample,
                    end_sample
                )

                added_count += 1
                print(f"成功添加分段 {i+1}: class_id={current_class_id}, class_name={class_name}, file_path={file_path}")
                print(f"  参数: fs={fs_val/1e6:.2f}MHz, bw={bw_val/1e3:.1f}kHz")

            except Exception as e:
                error_msg = f"分段 {i+1}: {str(e)}"
                errors.append(error_msg)
                print(f"添加分段 {i+1} 失败: {e}")
                continue
        
        # 准备返回结果
        if added_count > 0:
            success_msg = f"成功添加 {added_count} 个分段"
            if errors:
                success_msg += f"，失败 {len(errors)} 个"

            # 添加文件复制结果信息
            if copied_file_path:
                success_msg += f"\n源文件已备份到: {copied_file_path}"
            elif original_file_path:
                success_msg += f"\n注意: 源文件复制失败，但数据库添加成功"

            print(success_msg)
            return True, added_count, ""
        else:
            error_msg = "所有分段都添加失败"
            if errors:
                error_msg += f": {'; '.join(errors[:3])}"  # 只显示前3个错误
            return False, 0, error_msg
            
    except Exception as e:
        import traceback
        return False, 0, f"批量添加分段失败: {str(e)}\n{traceback.format_exc()}"


def get_database_structure():
    """
    获取数据库结构信息
    
    Returns:
        tuple: (success, db_info_list, error_msg)
    """
    try:
        from usrlib.usrlib import get_config_path
        fname = get_config_path()

        if not os.path.exists(fname):
            return False, [], "数据库文件不存在"
        
        # 获取数据库内容（包含新字段）
        db_result = GetArcVectorDB()
        if len(db_result) == 9:  # 最新版本数据库（包含record_id）
            vectors, record_ids, clsids, clsnames, filepaths, curfs, curbw, start_samples, end_samples = db_result
        elif len(db_result) == 8:  # 中间版本数据库（包含样本字段但无record_id）
            vectors, clsids, clsnames, filepaths, curfs, curbw, start_samples, end_samples = db_result
            # 为向后兼容，使用class_id作为record_id
            record_ids = clsids.copy()
        else:  # 旧版本数据库（向后兼容）
            vectors, clsids, clsnames, filepaths, curfs, curbw = db_result
            start_samples = np.zeros((clsids.shape[0], 1), dtype=np.int64)
            end_samples = np.zeros((clsids.shape[0], 1), dtype=np.int64)
            # 为向后兼容，使用class_id作为record_id
            record_ids = clsids.copy()
        
        if vectors is None or len(vectors) == 0:
            return False, [], "数据库为空"
        
        # 构建记录列表，每个记录是一个元组，用于TreeView显示
        db_records = []
        
        for i in range(len(vectors)):
            # 安全地提取数据，处理可能的数组格式
            # 提取record_id（数据编号）
            record_id = 0
            if record_ids is not None and i < len(record_ids):
                rid = record_ids[i]
                if hasattr(rid, '__len__') and len(rid) > 0:
                    record_id = int(rid[0])
                else:
                    record_id = int(rid) if rid is not None else 0

            # 提取class_id（类别标识符）
            class_id = 0
            if clsids is not None and i < len(clsids):
                cid = clsids[i]
                if hasattr(cid, '__len__') and len(cid) > 0:
                    class_id = int(cid[0])
                else:
                    class_id = int(cid) if cid is not None else 0
            
            class_name = ""
            if clsnames is not None and i < len(clsnames):
                cname = clsnames[i]
                if hasattr(cname, '__len__') and len(cname) > 0:
                    class_name = str(cname[0])
                else:
                    class_name = str(cname) if cname is not None else ""
            # 清理类名字符串
            class_name = clean_class_name(class_name)

            file_path = ""
            if filepaths is not None and i < len(filepaths):
                fpath = filepaths[i]
                if hasattr(fpath, '__len__') and len(fpath) > 0:
                    file_path = str(fpath[0])
                else:
                    file_path = str(fpath) if fpath is not None else ""
            # 清理文件路径字符串
            file_path = clean_path_string(file_path)
            
            sample_rate = "0.0 MHz"
            if curfs is not None and i < len(curfs):
                fs = curfs[i]
                if hasattr(fs, '__len__') and len(fs) > 0:
                    fs_val = float(fs[0])
                else:
                    fs_val = float(fs) if fs is not None else 0.0
                sample_rate = f"{fs_val/1e6:.2f} MHz"
            
            bandwidth = "0.0 MHz"
            if curbw is not None and i < len(curbw):
                bw = curbw[i]
                if hasattr(bw, '__len__') and len(bw) > 0:
                    bw_val = float(bw[0])
                else:
                    bw_val = float(bw) if bw is not None else 0.0
                bandwidth = f"{bw_val/1e6:.2f} MHz"

            # 提取分段位置信息
            start_sample = 0
            if start_samples is not None and i < len(start_samples):
                start = start_samples[i]
                if hasattr(start, '__len__') and len(start) > 0:
                    start_sample = int(start[0])
                else:
                    start_sample = int(start) if start is not None else 0

            end_sample = 0
            if end_samples is not None and i < len(end_samples):
                end = end_samples[i]
                if hasattr(end, '__len__') and len(end) > 0:
                    end_sample = int(end[0])
                else:
                    end_sample = int(end) if end is not None else 0

            # 创建记录元组（包含record_id和分段位置信息）
            record = (record_id, class_id, class_name, file_path, sample_rate, bandwidth, start_sample, end_sample)
            db_records.append(record)
        
        return True, db_records, ""
        
    except Exception as e:
        return False, [], f"读取数据库结构失败: {str(e)}"


def create_modified_proc_wbsig(signal, wb_fs, wb_fc, wb_bw, no_threshold=True):
    """
    修改版的proc_wbsig函数，可以选择不进行阈值限制

    Args:
        signal: 输入信号
        wb_fs: 采样率
        wb_fc: 中心频率
        wb_bw: 带宽
        no_threshold: 是否不进行阈值限制
    """
    try:
        # 这里可以复制proc_wbsig的代码并修改阈值部分
        # 为了简化，直接调用原函数
        # 如果需要修改阈值限制，需要修改calregions.py中的相关代码
        proc_wbsig(signal, wb_fs, wb_fc, wb_bw)
        return True, ""
    except Exception as e:
        return False, f"信道扫描失败: {str(e)}"


def extract_segment_from_file(file_path, start_sample, end_sample, file_format=None):
    """
    从文件中提取指定分段的信号数据

    Args:
        file_path (str): 信号文件路径
        start_sample (int): 起始样本位置
        end_sample (int): 结束样本位置
        file_format (str): 文件格式，如果为None则自动检测

    Returns:
        tuple: (success, segment_signal, metadata, error_msg)
            - success (bool): 是否成功提取
            - segment_signal (np.array): 分段信号数据（复数格式）
            - metadata (dict): 信号元数据
            - error_msg (str): 错误信息
    """
    try:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            return False, None, None, f"文件不存在: {file_path}"

        # 自动检测文件格式
        if file_format is None:
            file_format = os.path.splitext(file_path)[1].lower()

        # 根据文件格式读取信号
        if file_format in ['.bvsp', '.dat']:
            # 使用Read_sigfile读取
            result = Read_sigfile(file_path, [0, -1])
            if result is None:
                return False, None, None, f"无法读取文件: {file_path}"

            sigdata, samp_rate, center_freq, bandwidth = result
            # 转换为复数信号
            full_signal = sigdata[0, :, 0] + 1j * sigdata[0, :, 1]

            metadata = {
                'fs': float(samp_rate),
                'fc': float(center_freq),
                'bw': float(bandwidth),
                'file_format': file_format
            }

        elif file_format == '.hdfv':
            # 使用read_signal_from_hdf5读取
            full_signal, hdf_metadata = read_signal_from_hdf5(file_path)

            metadata = {
                'fs': float(hdf_metadata.get('fs', 61.44e6)),
                'fc': float(hdf_metadata.get('fc', 915e6)),
                'bw': float(hdf_metadata.get('bw', 50e6)),
                'file_format': file_format
            }
        else:
            return False, None, None, f"不支持的文件格式: {file_format}"

        # 验证分段位置
        signal_length = len(full_signal)
        if start_sample < 0:
            start_sample = 0
        if end_sample <= start_sample or end_sample > signal_length:
            end_sample = signal_length

        # 提取分段
        segment_signal = full_signal[start_sample:end_sample]

        # 更新元数据
        metadata.update({
            'segment_start': start_sample,
            'segment_end': end_sample,
            'segment_length': len(segment_signal),
            'total_length': signal_length
        })

        return True, segment_signal, metadata, ""

    except Exception as e:
        return False, None, None, f"提取信号分段失败: {str(e)}"


def backup_database():
    """
    备份数据库文件

    Returns:
        tuple: (success, backup_path, error_msg)
    """
    try:
        from usrlib.usrlib import get_config_path
        import shutil
        from datetime import datetime

        # 获取数据库文件路径
        db_path = get_config_path()
        if not os.path.exists(db_path):
            return False, None, "数据库文件不存在，无需备份"

        # 生成备份文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_dir = os.path.dirname(db_path)
        backup_filename = f"sig-vectordB_backup_{timestamp}.cfg"
        backup_path = os.path.join(backup_dir, backup_filename)

        # 复制文件
        shutil.copy2(db_path, backup_path)

        print(f"数据库已备份到: {backup_path}")
        return True, backup_path, ""

    except Exception as e:
        return False, None, f"备份数据库失败: {str(e)}"


def restore_database(backup_path):
    """
    从备份恢复数据库文件

    Args:
        backup_path (str): 备份文件路径

    Returns:
        tuple: (success, error_msg)
    """
    try:
        from usrlib.usrlib import get_config_path
        import shutil

        if not os.path.exists(backup_path):
            return False, f"备份文件不存在: {backup_path}"

        # 获取数据库文件路径
        db_path = get_config_path()

        # 恢复文件
        shutil.copy2(backup_path, db_path)

        print(f"数据库已从备份恢复: {backup_path}")
        return True, ""

    except Exception as e:
        return False, f"恢复数据库失败: {str(e)}"


def update_database_vectors(model, progress_callback=None, update_class_ids=False):
    """
    使用新模型更新数据库中所有记录的特征向量，可选择同时更新类别ID

    Args:
        model: 新的Arcface模型
        progress_callback: 进度回调函数，接收参数(current, total, message)
        update_class_ids: 是否同时更新类别ID（基于文件路径推断）

    Returns:
        tuple: (success, updated_count, failed_count, error_msg)
    """
    logger = get_logger()
    logger.info("开始更新数据库特征向量")

    try:
        from usrlib.usrlib import get_config_path
        import h5py
        import tempfile
        from utils.dataloader import getLenNorm

        # 检查模型
        if model is None:
            logger.error("模型为空，无法进行更新")
            return False, 0, 0, "模型不能为空"

        # 获取数据库路径
        db_path = get_config_path()
        logger.info(f"数据库路径: {db_path}")

        if not os.path.exists(db_path):
            logger.error(f"数据库文件不存在: {db_path}")
            return False, 0, 0, "数据库文件不存在"

        # 备份数据库
        logger.info("开始备份数据库")
        if progress_callback:
            progress_callback(0, 0, "正在备份数据库...")

        backup_success, backup_path, backup_error = backup_database()
        if not backup_success:
            logger.error(f"备份数据库失败: {backup_error}")
            return False, 0, 0, f"备份数据库失败: {backup_error}"

        logger.info(f"数据库备份成功: {backup_path}")

        # 读取现有数据库
        if progress_callback:
            progress_callback(0, 0, "正在读取数据库记录...")

        db_result = GetArcVectorDB()
        if db_result is None:
            return False, 0, 0, "无法读取数据库"

        # 兼容新旧数据库格式
        if len(db_result) == 9:  # 最新版本数据库（包含record_id）
            vectors, record_ids, clsids, clsnames, filepaths, curfs, curbw, start_samples, end_samples = db_result
        elif len(db_result) == 8:  # 中间版本数据库（包含样本字段但无record_id）
            vectors, clsids, clsnames, filepaths, curfs, curbw, start_samples, end_samples = db_result
            # 为向后兼容，使用class_id作为record_id
            record_ids = clsids.copy()
        else:  # 旧版本数据库
            vectors, clsids, clsnames, filepaths, curfs, curbw = db_result
            start_samples = np.zeros((clsids.shape[0], 1), dtype=np.int64)
            end_samples = np.zeros((clsids.shape[0], 1), dtype=np.int64)
            # 为向后兼容，使用class_id作为record_id
            record_ids = clsids.copy()

        total_records = len(clsids)
        if total_records == 0:
            return False, 0, 0, "数据库为空"

        # 获取期望的信号长度
        expected_length = getLenNorm()

        # 创建临时数据库文件
        temp_db_path = db_path + ".tmp"
        updated_count = 0
        failed_count = 0
        failed_records = []

        try:
            # 获取第一个记录的特征向量维度（用于创建数据库）
            first_vector_dim = None

            # 处理第一条记录以确定特征向量维度
            for i in range(total_records):
                if progress_callback:
                    progress_callback(i, total_records, f"正在处理记录 {i+1}/{total_records}")

                try:
                    # 获取记录信息
                    class_id = int(clsids[i][0]) if hasattr(clsids[i], '__len__') else int(clsids[i])
                    class_name = str(clsnames[i][0]) if hasattr(clsnames[i], '__len__') else str(clsnames[i])
                    file_path = str(filepaths[i][0]) if hasattr(filepaths[i], '__len__') else str(filepaths[i])
                    fs_val = float(curfs[i][0]) if hasattr(curfs[i], '__len__') else float(curfs[i])
                    bw_val = float(curbw[i][0]) if hasattr(curbw[i], '__len__') else float(curbw[i])

                    start_sample = int(start_samples[i][0]) if hasattr(start_samples[i], '__len__') else int(start_samples[i])
                    end_sample = int(end_samples[i][0]) if hasattr(end_samples[i], '__len__') else int(end_samples[i])

                    # 清理字符串
                    class_name = clean_class_name(class_name)
                    file_path = clean_path_string(file_path)

                    # 提取信号分段
                    success, segment_signal, metadata, error_msg = extract_segment_from_file(
                        file_path, start_sample, end_sample
                    )

                    if not success:
                        failed_count += 1
                        failed_records.append(f"记录 {i+1}: {error_msg}")
                        continue

                    # 预处理信号数据 - 与add_segments_to_db保持一致
                    if np.iscomplexobj(segment_signal):
                        # 如果是复数数组，分离I和Q分量
                        if segment_signal.ndim == 1:
                            # 一维复数数组 [N] -> [N, 2]
                            i_component = np.real(segment_signal)
                            q_component = np.imag(segment_signal)
                            segment_signal_data = np.column_stack([i_component, q_component])
                        else:
                            raise ValueError(f"不支持的复数信号维度: {segment_signal.shape}")
                    else:
                        # 如果是实数数组，检查维度
                        if segment_signal.ndim == 2:
                            if segment_signal.shape[0] == 2:  # [2, N] -> [N, 2]
                                segment_signal_data = segment_signal.T
                            elif segment_signal.shape[1] != 2:  # 不是 [N, 2] 格式
                                raise ValueError(f"实数信号数据应为 [N, 2] 或 [2, N] 格式，实际: {segment_signal.shape}")
                            else:
                                segment_signal_data = segment_signal
                        else:
                            raise ValueError(f"不支持的实数信号维度: {segment_signal.shape}")

                    print(f"转换后信号形状: {segment_signal_data.shape}")

                    # 调整信号长度
                    current_length = segment_signal_data.shape[0]
                    if current_length != expected_length:
                        if current_length > expected_length:
                            # 截取中间部分
                            start_idx = (current_length - expected_length) // 2
                            segment_signal_data = segment_signal_data[start_idx:start_idx + expected_length]
                        else:
                            # 零填充
                            padding = expected_length - current_length
                            pad_before = padding // 2
                            pad_after = padding - pad_before
                            segment_signal_data = np.pad(segment_signal_data, ((pad_before, pad_after), (0, 0)), 'constant')

                    # 确保数据是连续的并转换为float32
                    segment_signal_data = np.ascontiguousarray(segment_signal_data.astype(np.float32))

                    # 模型推理
                    sig_tensor = torch.from_numpy(segment_signal_data).unsqueeze(0)
                    if torch.cuda.is_available():
                        sig_tensor = sig_tensor.cuda()

                    with torch.no_grad():
                        vec = model(sig_tensor)
                        vec = vec.squeeze()

                    # 获取特征向量
                    feature_vector = vec.detach().cpu().numpy()

                    # 确定特征向量维度（只在第一次）
                    if first_vector_dim is None:
                        first_vector_dim = feature_vector.shape[0]

                        # 创建临时数据库文件（启用分块以支持resize）
                        h5f = h5py.File(temp_db_path, "w")

                        # 创建可调整大小的数据集（启用分块）- 包含新的record_id字段
                        v1 = h5f.create_dataset("sigvector", (total_records, first_vector_dim),
                                               dtype=np.float32, maxshape=(None, first_vector_dim),
                                               chunks=True)
                        v2 = h5f.create_dataset("record_id", (total_records, 1),
                                               dtype=np.int32, maxshape=(None, 1),
                                               chunks=True)
                        v3 = h5f.create_dataset("classid", (total_records, 1),
                                               dtype=np.int32, maxshape=(None, 1),
                                               chunks=True)
                        dtstr = h5py.special_dtype(vlen=str)
                        v4 = h5f.create_dataset("classname", (total_records, 1),
                                               dtype=dtstr, maxshape=(None, 1),
                                               chunks=True)
                        v5 = h5f.create_dataset("filepath", (total_records, 1),
                                               dtype=dtstr, maxshape=(None, 1),
                                               chunks=True)
                        v6 = h5f.create_dataset("fs", (total_records, 1),
                                               dtype=np.float32, maxshape=(None, 1),
                                               chunks=True)
                        v7 = h5f.create_dataset("bw", (total_records, 1),
                                               dtype=np.float32, maxshape=(None, 1),
                                               chunks=True)
                        v8 = h5f.create_dataset("start_sample", (total_records, 1),
                                               dtype=np.int64, maxshape=(None, 1),
                                               chunks=True)
                        v9 = h5f.create_dataset("end_sample", (total_records, 1),
                                               dtype=np.int64, maxshape=(None, 1),
                                               chunks=True)

                    # 获取原始的record_id和class_id
                    original_record_id = int(record_ids[i][0]) if hasattr(record_ids[i], '__len__') else int(record_ids[i])
                    original_class_id = int(clsids[i][0]) if hasattr(clsids[i], '__len__') else int(clsids[i])

                    # 根据选项决定是否更新class_id
                    if update_class_ids:
                        # 导入class_id推断函数
                        try:
                            import sys as sys_module
                            import os as os_module
                            current_dir = os_module.path.dirname(os_module.path.abspath(__file__))
                            parent_dir = os_module.path.dirname(current_dir)
                            if parent_dir not in sys_module.path:
                                sys_module.path.append(parent_dir)

                            from usrlib.usrlib import infer_class_id_from_path
                            updated_class_id = infer_class_id_from_path(file_path)

                            if updated_class_id != original_class_id:
                                logger.info(f"记录 {i+1}: class_id从 {original_class_id} 更新为 {updated_class_id}")
                        except Exception as e:
                            error_msg = str(e)
                            if "pathsetting.json" in error_msg or "路径配置文件" in error_msg:
                                # 如果是配置文件问题，提示用户选择class_def.txt文件
                                logger.error(f"配置文件问题，需要手动选择class_def.txt文件: {e}")
                                # 这里可以添加文件选择对话框的逻辑
                                # 暂时保持原值，让用户知道需要配置文件
                                updated_class_id = original_class_id
                                # 可以设置一个标志，在更新完成后提示用户配置文件问题
                            else:
                                logger.warning(f"推断class_id失败，保持原值: {e}")
                                updated_class_id = original_class_id
                    else:
                        updated_class_id = original_class_id

                    # 写入数据到临时数据库
                    v1[updated_count] = feature_vector
                    v2[updated_count] = original_record_id  # 保持原始record_id
                    v3[updated_count] = updated_class_id    # 使用更新后的class_id
                    v4[updated_count] = class_name
                    v5[updated_count] = file_path
                    v6[updated_count] = fs_val
                    v7[updated_count] = bw_val
                    v8[updated_count] = start_sample
                    v9[updated_count] = end_sample

                    updated_count += 1

                except Exception as e:
                    failed_count += 1
                    failed_records.append(f"记录 {i+1}: {str(e)}")
                    continue

            # 关闭临时数据库文件
            if 'h5f' in locals():
                h5f.close()

            # 如果有成功更新的记录，替换原数据库
            if updated_count > 0:
                # 调整临时数据库大小
                with h5py.File(temp_db_path, "a") as h5f:
                    h5f["sigvector"].resize((updated_count, first_vector_dim))
                    h5f["record_id"].resize((updated_count, 1))  # 修复：添加缺失的record_id字段
                    h5f["classid"].resize((updated_count, 1))
                    h5f["classname"].resize((updated_count, 1))
                    h5f["filepath"].resize((updated_count, 1))
                    h5f["fs"].resize((updated_count, 1))
                    h5f["bw"].resize((updated_count, 1))
                    h5f["start_sample"].resize((updated_count, 1))
                    h5f["end_sample"].resize((updated_count, 1))

                # 替换原数据库
                import shutil
                shutil.move(temp_db_path, db_path)

                if progress_callback:
                    progress_callback(total_records, total_records, "更新完成")

                # 构建结果消息
                result_msg = f"成功更新 {updated_count} 条记录"
                if failed_count > 0:
                    result_msg += f"，失败 {failed_count} 条记录"
                    if failed_records:
                        result_msg += f"\n失败详情:\n" + "\n".join(failed_records[:5])  # 只显示前5个错误
                        if len(failed_records) > 5:
                            result_msg += f"\n... 还有 {len(failed_records) - 5} 个错误"

                return True, updated_count, failed_count, result_msg
            else:
                # 没有成功更新任何记录，恢复备份
                restore_database(backup_path)
                return False, 0, failed_count, f"没有成功更新任何记录，已恢复原数据库。错误详情:\n" + "\n".join(failed_records[:5])

        except Exception as e:
            # 发生异常，清理临时文件并恢复备份
            if os.path.exists(temp_db_path):
                os.remove(temp_db_path)
            restore_database(backup_path)
            return False, updated_count, failed_count, f"更新过程中发生异常: {str(e)}"

    except Exception as e:
        return False, 0, 0, f"更新数据库失败: {str(e)}"