# =======================================================================================================================
#   Function    ：arcface.py
#   Description : 模型总体集成类
# 
#   Parameter   : 
#   Author      : Liuzhiguo
#   Date        : 2025-1-20
# =======================================================================================================================
import math
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.nn import Module, Parameter

from nets.iresnet import (iresnet18, iresnet34, iresnet50, iresnet100,
                          iresnet200)
from nets.mobilefacenet import get_mbf
from nets.mobilenet import get_mobilenet
from nets.DroneSigNet import DroneSigNet 
from nets.swin_transformer import swin_transformer_tiny
from nets.Module_attentions import SE_Row, SE_RowV1
'''
20250424上传版本：
此版本中参考：
1. SUbCenter_Arcface_Head类用于分类任务，可能会对验证集损失下降有所助益
2. 信号处理参考Arcface类中的def Temporal_Normalization对信号进行能量归一化并在forward的第一步就进行归一化操作
3. 减小分类头dimesion 并且引入非线性变化
'''

class Arcface_Head(Module):
    def __init__(self, embedding_size=128, num_classes=10575, s=64., m=0.5):
        super(Arcface_Head, self).__init__()
        self.s = s
        self.m = m
        self.weight = Parameter(torch.FloatTensor(num_classes, embedding_size))   #weights矩阵的作用是将信号映射至分类空间
        nn.init.xavier_uniform_(self.weight)

        self.cos_m = math.cos(m)
        self.sin_m = math.sin(m)
        self.th = math.cos(math.pi - m)
        self.mm = math.sin(math.pi - m) * m

    def forward(self, input, label):
        # 存在问题:Softmax loss 的改造，相当于在分子分母上同时减小一个值，破坏了 Softmax 总和为1的特性
        # 角度距离
        cosine  = F.linear(input, F.normalize(self.weight, dim=1)) # (C, N) 类内每个特征维度归一化
        sine    = torch.sqrt((1.0 - torch.pow(cosine, 2)).clamp(0, 1))
        phi     = cosine * self.cos_m - sine * self.sin_m  #cos(theta+m)
        phi     = torch.where(cosine.float() > self.th, phi.float(), cosine.float() - self.mm)  #如论文中所讲 对于特殊情况要额外考虑theta+m>=pi

        one_hot = torch.zeros(cosine.size()).type_as(phi).long()  #(0000010000)这种类似的onehot编码
        one_hot.scatter_(1, label.view(-1, 1).long(), 1)
        output  = (one_hot * phi) + ((1.0 - one_hot) * cosine) #假设是1类，则在1类别上去变成1*cosine(theta+m)，0类别上还是cosine(theta)
        output  *= self.s  #输出是一个b*num_class
        return output

class SubCenter_Arcface_Head(Module):
    def __init__(self, embedding_size=128, num_classes=10575, s=64., m=0.5, k=3):
        super(SubCenter_Arcface_Head, self).__init__()
        self.s = s
        self.m = m
        self.k = k  # 每个类的子中心数量
        # 权重矩阵扩展为 [num_classes * k, embedding_size]
        self.weight = Parameter(torch.FloatTensor(num_classes * k, embedding_size))
        nn.init.xavier_uniform_(self.weight)

        self.cos_m = math.cos(m)
        self.sin_m = math.sin(m)
        self.th = math.cos(math.pi - m)
        self.mm = math.sin(math.pi - m) * m

    def forward(self, input, label):
        cosine = F.linear(input, F.normalize(self.weight))  # [N, num_classes * k]
        cosine = cosine.view(-1, self.k, cosine.size(-1) // self.k)  # [N, k, num_classes]

        # 对每个类选择最大的子中心余弦值
        cosine, _ = torch.max(cosine, dim=1)  # [N, num_classes]

        sine = torch.sqrt((1.0 - torch.pow(cosine, 2)).clamp(0, 1))
        phi = cosine * self.cos_m - sine * self.sin_m
        phi = torch.where(cosine.float() > self.th, phi.float(), cosine.float() - self.mm)

        one_hot = torch.zeros(cosine.size()).type_as(phi).long()
        one_hot.scatter_(1, label.view(-1, 1).long(), 1)
        output = (one_hot * phi) + ((1.0 - one_hot) * cosine)
        output *= self.s

        return output
    
class Arcface(nn.Module):
    def __init__(self, num_classes=None, backbone="mobilefacenet", pretrained=False, mode="train", ModelType = 0):
        super(Arcface, self).__init__()
        self.in_ch = 8
        #self.weights = torch.tensor([0.5, 0.4, 0.1], requires_grad=True)
        self.weights = [0.2, 0.4, 0.3, 0.1]
        #self.max_pooling_layer = nn.MaxPool2d(kernel_size=(self.in_ch, 1), stride=(self.in_ch, 1))
        #self.max_pooling_layer = nn.AdaptiveMaxPool2d([128, 116])
        self.conv_pooling_layer = nn.Conv2d(1, 1, kernel_size=[self.in_ch,1], stride=[self.in_ch, 1], bias=False)
        self.conv_emb = nn.Conv2d(self.in_ch, self.in_ch, kernel_size=[3,3], padding=1) #向量嵌入
        self.se_row_layer = SE_Row(in_chnls=3, ratio=1)  # 平均值加权
        #self.se_row_layer = SE_RowV1(in_chnls=3, ratio=1) # 平均值和最大值加权
        if backbone=="mobilefacenet":
            embedding_size  = 128
            s               = 32
            self.arcface    = get_mbf(embedding_size=embedding_size, pretrained=pretrained)

        elif backbone=="mobilenetv1":
            embedding_size  = 512
            s               = 64
            self.arcface    = get_mobilenet(dropout_keep_prob=0.5, embedding_size=embedding_size, pretrained=pretrained)

        elif backbone=="iresnet18":
            embedding_size  = 512
            s               = 64
            self.arcface    = iresnet18(dropout_keep_prob=0.5, embedding_size=embedding_size, pretrained=pretrained)

        elif backbone=="iresnet34":
            embedding_size  = 512
            s               = 64
            self.arcface    = iresnet34(dropout_keep_prob=0.5, embedding_size=embedding_size, pretrained=pretrained)

        elif backbone=="iresnet50":
            embedding_size  = 512
            s               = 64
            self.arcface    = iresnet50(dropout_keep_prob=0.5, embedding_size=embedding_size, pretrained=pretrained)

        elif backbone=="iresnet100":
            embedding_size  = 512
            s               = 64
            self.arcface    = iresnet100(dropout_keep_prob=0.5, embedding_size=embedding_size, pretrained=pretrained)

        elif backbone=="iresnet200":
            embedding_size  = 512
            s               = 64
            self.arcface    = iresnet200(dropout_keep_prob=0.5, embedding_size=embedding_size, pretrained=pretrained)
        elif backbone=="DroneSigNet":
            if ModelType==0:
                embedding_size  = 64#96#512  512
            else:
                embedding_size  = 64#96#512
                
            s               = 64
            self.arcface    = DroneSigNet(embedding_size=embedding_size) 
        elif backbone=="swin_transformer_tiny":
            embedding_size  = 512
            s               = 64
            self.arcface    = swin_transformer_tiny(input_shape=[1024, 45],pretrained=False,num_classes=embedding_size)           
        else:
            raise ValueError('Unsupported backbone - `{}`, Use mobilefacenet, mobilenetv1.'.format(backbone))

        self.mode = mode
        #if mode == "train":#匹配头
        self.ArcHead = Arcface_Head(embedding_size=embedding_size, num_classes=num_classes, s=s)
		#todo：20250421 这里尝试下子空间ArcHead
        #self.head = SubCenter_Arcface_Head(embedding_size=embedding_size, num_classes=num_classes, s=s)
        #self.layer_norm = nn.LayerNorm(embedding_size)
        #分类头 (分类用)
        self.ClsHead = nn.Sequential(
            nn.Dropout(p=0),#0.2
            nn.Linear(embedding_size, embedding_size//2),
            #note:4/10 尝试引入非线性
            nn.ELU(),
            nn.Linear(embedding_size//2, num_classes),
        )

        self.HeaderType = ModelType

    def preProcess(self, rx_signals):
        # 区别: 权重为预设值，不进行修改
        #  1. 计算stft 
        N_fft = 1024 #self.subcarriers
        N_window = N_fft  
        N_overlap = math.floor(N_fft/2) 
        if len(rx_signals.shape)>3:
            y = rx_signals.squeeze() # [64, 1, 59392, 2] --> [64, 59392, 2]
        else:
            y = rx_signals
        z = torch.complex(y[:,:,0], y[:,:,1]) # 合成复数 [64, 59392]
        hann_window = torch.hann_window(N_fft, periodic=True, device=z.device)
        z = torch.stft(input=z, n_fft=N_fft, window=hann_window,hop_length=N_overlap, win_length=N_window,center=False,normalized=True) #转换到频域  
        z = torch.fft.fftshift(z, dim=1) #[batch, fft_size, timeframe]

        z = torch.abs(z).unsqueeze(-1) # torch.Size([64, 1024, 116, 1])
        z = z.permute(0, 3, 1, 2)     #[batch, abs(I/Q), fft_size, timeframe]

        # 缩小化抽取
        #z_sub = F.interpolate(z,size=[nCarriers//8, nTimesym],mode='bilinear')
        z_sub = self.conv_pooling_layer(z)

        # 2 在时间维度，数据分为4块，分别设置不同权重
        chunks = torch.chunk(z, 4, dim=-1)
        #信号分成3段，分别赋予不同权重
        # 为每块赋予权重
        weighted_chunks = [chunk * weight for chunk, weight in zip(chunks, self.weights)]
        # 重新组合
        z = torch.cat(weighted_chunks, dim=-1)

        nbatch, _ , nCarriers, nTimesym = z.shape
        n_subcount = math.floor(N_fft/self.in_ch)
        z = torch.reshape(z,(nbatch, self.in_ch, n_subcount, nTimesym))
        z = self.conv_emb(z)             # word embedding
        z = torch.cat((z, z_sub), dim=1) # 增加一个全局维度

        return z

    def preProcessExt(self, rx_signals):
        # 1. 计算stft
        N_fft = 1024 #self.subcarriers
        N_window = N_fft  
        N_overlap = math.floor(N_fft/4) 
        if len(rx_signals.shape)>3:
            y = rx_signals.squeeze() # [64, 1, 59392, 2] --> [64, 59392, 2]
        else:
            y = rx_signals
        z = torch.complex(y[:,:,0], y[:,:,1]) # 合成复数 [64, 59392]  #note： 原本保存是实部虚部分开 但是最终操作起来还是属于复数
        #hann_window = torch.hann_window(N_fft, periodic=True, device=z.device)
        #使用不同的窗，频域曲线的平滑程度，不同频率分量信息的清晰程度均有区别。
        # Blackman Window 的效果相对较好，频域曲线相对比较平滑并且不同频率分量的信息也比较清晰。
        # 从频域可以看到，主瓣宽度 6 格， 旁瓣最高值 -58 dB
        blackman_window = torch.blackman_window(N_fft, periodic=True, device=z.device)
        # 参考 https://blog.csdn.net/weixin_44618906/article/details/116356081
        #
        z = torch.stft(input=z, n_fft=N_fft, window=blackman_window, hop_length=N_overlap, win_length=N_window, center=False, normalized=True) #转换到频域  
        #与matlab中centered参数不同，这里参数 center 默认为true，输入会在两侧pad，pad长度为n_fft // 2
        #pad的value不是0，默认pad_mode 为‘reflect’ ，即为反射，如果输入的信号为：1，2，3，4，5，6， 在两侧pad  n_fft // 2 = 3个，则pad之后的结果为：
        # 4，3，2，【1，2，3，4，5，6】，5，4，3
        z = torch.fft.fftshift(z, dim=1) #[batch, fft_size, timeframe]

        #
        # 1 交换维度， stft已经在输入数据前的预处理部分实现
        # 输入格式为 [batch, fft_size, timeframe, abs(I/Q)] 
        z = torch.abs(z).unsqueeze(-1) # torch.Size([64, 1024, 45, 1])
        z = z.permute(0, 3, 1, 2)     # 输出格式为 [batch, abs(I/Q), fft_size, timeframe]


        # 2 缩小化抽取
        z_reduced = self.conv_pooling_layer(z)   # size=[batch, abs(I/Q), fft_size//8, nTimesym]

        # 3 在时间维度，数据分为3块，分别设置不同权重
        # 3.1 信号分成3段，分别赋予不同权重
        chunks = torch.chunk(z, 3, dim=-1)       # [batch, abs(I/Q), fft_size, timeframe/3]
        chunks = torch.cat(chunks, dim=1)        # 第1个维度拼接 [batch, 3, fft_size, timeframe/3]
        
        #信号分成3段，分别赋予不同权重
        # 3.2 为每块应用注意力机制        
        weighted_chunks = self.se_row_layer(chunks)  #explain:这里是获得在不同频谱之间的注意力分数（这里一个很好的方式就是将时域划分为3部分，不同时间范围中重点关注的频点可能是不同的，因此使用Group COnv分别对三组独立求得关于频段的注意力分数# ）
        weighted_chunks = torch.chunk(weighted_chunks, 3, dim=1)  # 拆分
        #weighted_chunks = [chunk * weight for chunk, weight in zip(chunks, self.weights)]
        # 3.3 重新组合
        z = torch.cat(weighted_chunks, dim=-1)
        
        nbatch, _ , nCarriers, nTimesym = z.shape
		# 4 重新分解为多个channel
        n_subcount = math.floor(N_fft/self.in_ch)
        z = torch.reshape(z,(nbatch, self.in_ch, n_subcount, nTimesym)) # reshape为多个channel，主要目的为：节省计算量

        z = self.conv_emb(z)             # word embedding

        # 5 加入全局信息
        z = torch.cat((z, z_reduced), dim=1)                            # 防止丢失全局信息，增加一个全局维度channel(抽样缩小版本)
        # z = z + z_reduced # 论文Resource Efficient Perception for Vision Systems 表述， 全局块特征向量与其它块的特征融合采用相加的方式效果更佳
        # 个人测试效果一般（未采用此方法）
        return z
    
    def preProcessBig(self, rx_signals):
        #
        # 区别: 大图模式，1024不切分多个channel
        #
        # 1. 计算stft
        N_fft = 1024 #self.subcarriers
        N_window = N_fft  
        N_overlap = math.floor(N_fft/2) 
        if len(rx_signals.shape)>3:
            y = rx_signals.squeeze() # [64, 1, 59392, 2] --> [64, 59392, 2]
        else:
            y = rx_signals
        z = torch.complex(y[:,:,0], y[:,:,1]) # 合成复数 [64, 59392]
        hann_window = torch.hann_window(N_fft, periodic=True, device=z.device)
        z = torch.stft(input=z, n_fft=N_fft, window=hann_window,hop_length=N_overlap, win_length=N_window,normalized=False,center=False) #转换到频域  
        z = torch.fft.fftshift(z, dim=(-2, -1)) #[batch, fft_size, timeframe, I/Q]

        z = torch.abs(z).unsqueeze(-1) # torch.Size([64, 1024, 116, 1])
        z = z.permute(0, 3, 1, 2)     #[batch, abs(I/Q), fft_size, timeframe]
        z_last4col = z[:,:,:,-12:]
        z = torch.cat((z,z_last4col),dim=3)# torch.Size([64, 1024, 128, 1])

        return z

    # todo:2025/4/8: 这里尝试对时域信号进行归一化处理 看会不会最后的结果好一点
    #note:这里是功率归一化，结果显示可以
    def Temporal_normalization(self, rx_signals):
        signal_power = torch.sum((rx_signals[:, :, 0] ** 2 + rx_signals[:, :, 1] ** 2), dim=-1) / 2  #explain:得到信号的平均功率  所以这里要/2
        signal = rx_signals * math.sqrt(1) / torch.sqrt(signal_power.unsqueeze(-1).unsqueeze(-1))
        #todo：这里可以尝试下再减去一个均值20250421(就是实部虚部分别减去当前能量归一化后的均值) 结论：影响不大 先不加
        #signal = signal - torch.mean(signal, dim=1).unsqueeze(1)
        return signal
    def Temporal_normalization_Max(self, rx_signals):
        #exlpain:这里首先寻找到极径最大的点 然后进行最大值归一化
        max_radius = torch.max(torch.sqrt(rx_signals[:, :, 0] ** 2 + rx_signals[:, :, 1] ** 2), dim=-1)[0]
        signal = rx_signals / (max_radius.unsqueeze(-1).unsqueeze(-1) +1e-10) #避免除0
        return signal
    def Temporal_normal_l2(self,rx_signals):  #这个归一化的目的是使得一个信号中的所有点都映射单位圆上，
        signal = rx_signals / (torch.sqrt((rx_signals[:,:,0]**2+rx_signals[:,:,1]**2)).unsqueeze(-1) + 1e-10)  # 避免除0
        return signal
		
    def forward(self, x, y = None, mode = "predict"):
        # x：信号 格式为： [batch, fft_size, timeframe, 1] # 输入格式最后一维为 abs(I/Q)
        # y：标签值，只有在匹配模型训练时才会用到
        x = self.Temporal_normalization(x)
        x = self.preProcessExt(x) # （batch, 1024, 45, 1）#basic improved
    
        x = self.arcface(x) # 网络处理
        x = x.view(x.size()[0], -1)

        if self.HeaderType == 0:# 分类模型
            x = F.normalize(x, dim=1) #arcface-loss这里要求是对归一化之后的特征进行处理
            x = self.ClsHead(x) # [batch self.nclass] 
        else:                   # 匹配模型
            if mode == "predict": # 推理时用
                return x
            else:                 # 训练时用
                x = F.normalize(x, dim=1)
                #x = self.layer_norm(x)
                x = self.ArcHead(x, y) # 向量角距离递归，所以训练时也需要输入 y
        return x